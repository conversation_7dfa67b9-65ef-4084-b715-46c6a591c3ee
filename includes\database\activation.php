<?php
/**
 * Plugin Activation Functions
 *
 * @package RIDCODTOKEN
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Creates the necessary database tables upon plugin activation.
 */
function ridcodtoken_activate() {
	global $wpdb;
	require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );

	$charset_collate = $wpdb->get_charset_collate();
	$table_name_licenses = $wpdb->prefix . 'ridcod_licenses';
	$table_name_sites = $wpdb->prefix . 'ridcod_license_sites';

	// SQL for wp_ridcod_licenses table
	$sql_licenses = "CREATE TABLE $table_name_licenses (
		id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
		license_key varchar(255) NOT NULL,
		user_email varchar(255) DEFAULT NULL,
		status varchar(20) NOT NULL DEFAULT 'inactive',
		sites_allowed int(11) NOT NULL DEFAULT 1,
		order_id bigint(20) UNSIGNED DEFAULT NULL,
		product_id bigint(20) UNSIGNED DEFAULT NULL,
		created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
		expires_at datetime DEFAULT NULL,
		PRIMARY KEY  (id),
		UNIQUE KEY license_key (license_key)
	) $charset_collate;";

	dbDelta( $sql_licenses );

	// SQL for wp_ridcod_license_sites table
	$sql_sites = "CREATE TABLE $table_name_sites (
		id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
		license_id bigint(20) UNSIGNED NOT NULL,
		site_url varchar(255) NOT NULL,
		activated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
		PRIMARY KEY  (id),
		KEY license_id (license_id)
	) $charset_collate;";
	dbDelta( $sql_sites );

	// SQL for wp_ridcod_license_plans table
	$table_name_plans = $wpdb->prefix . 'ridcod_license_plans';
	$sql_plans = "CREATE TABLE $table_name_plans (
		id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
		plan_name varchar(255) NOT NULL,
		product_id bigint(20) UNSIGNED NOT NULL,
		sites_allowed int(11) NOT NULL DEFAULT 1,
		duration_days int(11) DEFAULT NULL,
		status varchar(20) NOT NULL DEFAULT 'active',
		created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
		updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		PRIMARY KEY  (id),
		UNIQUE KEY product_id (product_id),
		KEY status (status)
	) $charset_collate;";

	dbDelta( $sql_plans );

	   // Schedule the daily license check event if it's not already scheduled
	   if ( ! wp_next_scheduled( RIDCODTOKEN_CRON_HOOK ) ) {
	       // Schedule to run daily. The first run will be roughly 24 hours after activation.
	       // Consider scheduling for a specific time if needed (e.g., middle of the night).
	       wp_schedule_event( time(), 'daily', RIDCODTOKEN_CRON_HOOK );
	   }
}