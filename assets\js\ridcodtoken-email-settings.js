/**
 * RIDCODTOKEN Email Settings Admin JavaScript
 */

jQuery(document).ready(function($) {
    // التبديل بين معاينات الرسائل
    $('.preview-tab').on('click', function() {
        var tab = $(this).data('tab');
        
        $('.preview-tab').removeClass('active');
        $(this).addClass('active');
        
        $('.email-preview-content').removeClass('active');
        $('#' + tab + '-preview').addClass('active');
    });
    
    // تحديث المعاينة في الوقت الفعلي
    function updatePreview(type) {
        var subject = $('#' + type + '_email_subject').val();
        var content = '';
        
        if (type === 'purchase') {
            content = tinyMCE.get('purchase_email_content') ? 
                      tinyMCE.get('purchase_email_content').getContent() : 
                      $('#purchase_email_content').val();
        } else if (type === 'expiry') {
            content = tinyMCE.get('expiry_email_content') ? 
                      tinyMCE.get('expiry_email_content').getContent() : 
                      $('#expiry_email_content').val();
        }
        
        // تطبيق العناصر النائبة للمعاينة
        var previewSubject = subject;
        var previewContent = content;
        
        if (type === 'purchase') {
            previewSubject = previewSubject.replace(/{order_number}/g, '12345');
            previewSubject = previewSubject.replace(/{customer_name}/g, 'أحمد محمد');
            previewSubject = previewSubject.replace(/{site_name}/g, 'متجري');
              previewContent = previewContent.replace(/{license_keys}/g, 
                '<ul style="list-style: none; padding-left: 0; margin-bottom: 20px;">' +
                '<li style="margin-bottom: 10px; background-color: #f7f7f7; padding: 10px; border-radius: 4px;">' +
                '<strong>المنتج الأول:</strong><br>' +
                '<code style="font-size: 1.1em; background-color: #eee; padding: 3px 6px; border-radius: 3px; display: inline-block; margin-top: 5px;">ABCD-1234-EFGH-5678</code>' +
                '</li></ul>');
            previewContent = previewContent.replace(/{order_number}/g, '12345');
            previewContent = previewContent.replace(/{customer_name}/g, 'أحمد محمد');
            previewContent = previewContent.replace(/{site_name}/g, 'متجري');
        } else if (type === 'expiry') {
            previewSubject = previewSubject.replace(/{license_key}/g, 'ABCD-1234-EFGH-5678');
            previewSubject = previewSubject.replace(/{expiry_date}/g, '15 يوليو 2025');
            previewSubject = previewSubject.replace(/{customer_name}/g, 'أحمد محمد');
            previewSubject = previewSubject.replace(/{site_name}/g, 'متجري');
            
            previewContent = previewContent.replace(/{license_key}/g, 'ABCD-1234-EFGH-5678');
            previewContent = previewContent.replace(/{expiry_date}/g, '15 يوليو 2025');
            previewContent = previewContent.replace(/{customer_name}/g, 'أحمد محمد');
            previewContent = previewContent.replace(/{site_name}/g, 'متجري');
        }
        
        // تحديث المعاينة
        $('#' + type + '-preview .preview-subject').text(previewSubject);
        $('#' + type + '-preview .preview-content').html(previewContent);
    }
    
    // تحديث المعاينة عند تغيير المحتوى
    $('#purchase_email_subject').on('input', function() {
        updatePreview('purchase');
    });
    
    $('#expiry_email_subject').on('input', function() {
        updatePreview('expiry');
    });
    
    // تحديث المعاينة عند تغيير محتوى المحرر
    if (typeof tinyMCE !== 'undefined') {
        $(document).on('tinymce-editor-init', function(event, editor) {
            if (editor.id === 'purchase_email_content') {
                editor.on('input change', function() {
                    updatePreview('purchase');
                });
            } else if (editor.id === 'expiry_email_content') {
                editor.on('input change', function() {
                    updatePreview('expiry');
                });
            }
        });
    }
    
    // تحديث المعاينة عند تغيير محتوى textarea مباشرة
    $('#purchase_email_content').on('input', function() {
        updatePreview('purchase');
    });
    
    $('#expiry_email_content').on('input', function() {
        updatePreview('expiry');
    });
    
    // تحديث المعاينة الأولية
    updatePreview('purchase');
    updatePreview('expiry');
    
    // إضافة تأثيرات بصرية للنماذج
    $('.email-setting-section input[type="checkbox"]').on('change', function() {
        var section = $(this).closest('.email-setting-section');
        if ($(this).is(':checked')) {
            section.removeClass('disabled');
        } else {
            section.addClass('disabled');
        }
    });
    
    // تطبيق الحالة الأولية للأقسام
    $('.email-setting-section input[type="checkbox"]').each(function() {
        var section = $(this).closest('.email-setting-section');
        if (!$(this).is(':checked')) {
            section.addClass('disabled');
        }
    });
    
    // إضافة تأكيد للحفظ
    $('form').on('submit', function() {
        if (confirm('هل تريد حفظ إعدادات الرسائل؟')) {
            return true;
        }
        return false;
    });
    
    // إضافة وظيفة إعادة تعيين للإعدادات الافتراضية
    if ($('.reset-to-default').length === 0) {
        $('.submit').append('<button type="button" class="button reset-to-default" style="margin-right: 10px;">إعادة تعيين للافتراضي</button>');
    }
    
    $('.reset-to-default').on('click', function() {
        if (confirm('هل تريد إعادة تعيين جميع إعدادات الرسائل للقيم الافتراضية؟ سيتم فقدان جميع التخصيصات الحالية.')) {
            // إعادة تعيين رسالة الشراء
            $('#purchase_email_enabled').prop('checked', true);
            $('#purchase_email_subject').val('Your License Key(s) for Order #{order_number}');
            
            var defaultPurchaseContent = '<p>Thank you for your purchase!</p><p>Here are your license key(s):</p>{license_keys}<p>Please keep these keys safe.</p>';
            if (tinyMCE.get('purchase_email_content')) {
                tinyMCE.get('purchase_email_content').setContent(defaultPurchaseContent);
            } else {
                $('#purchase_email_content').val(defaultPurchaseContent);
            }
            
            // إعادة تعيين رسالة انتهاء الصلاحية
            $('#expiry_email_enabled').prop('checked', true);
            $('#expiry_email_subject').val('Your License Key is Expiring Soon');
            $('#expiry_email_days').val('7');
            
            var defaultExpiryContent = '<p>Hello,</p><p>This is a friendly reminder that your license key <strong>{license_key}</strong> is set to expire on {expiry_date}.</p><p>Please renew your license to continue receiving updates and support.</p><p>Thank you,<br>{site_name}</p>';
            if (tinyMCE.get('expiry_email_content')) {
                tinyMCE.get('expiry_email_content').setContent(defaultExpiryContent);
            } else {
                $('#expiry_email_content').val(defaultExpiryContent);
            }
            
            // تحديث المعاينة
            updatePreview('purchase');
            updatePreview('expiry');
            
            alert('تم إعادة تعيين الإعدادات للقيم الافتراضية.');
        }
    });
    
    // إضافة نصائح للمستخدم
    function showTooltips() {
        $('[data-tooltip]').each(function() {
            $(this).attr('title', $(this).data('tooltip'));
        });
    }    
    // إضافة إشعارات تفاعلية عند حفظ الإعدادات
    $('form[action*="ridcodtoken-email-settings"]').on('submit', function(e) {
        var form = $(this);
        var isTestEmail = form.find('input[name="send_test_email"]').length > 0;
        
        if (isTestEmail) {
            // التحقق من صحة البريد الإلكتروني للاختبار
            var testEmail = $('#test_email').val();
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            
            if (!emailRegex.test(testEmail)) {
                e.preventDefault();
                alert('يرجى إدخال عنوان بريد إلكتروني صحيح للاختبار.');
                $('#test_email').focus();
                return false;
            }
            
            if (!confirm('هل تريد إرسال رسالة تجريبية إلى ' + testEmail + '؟')) {
                e.preventDefault();
                return false;
            }
        }
    });
    
    // إضافة وظيفة نسخ العناصر النائبة إلى الحافظة
    $('code').on('click', function() {
        var text = $(this).text();
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(function() {
                // إظهار إشعار سريع
                var original = $(this);
                var originalText = original.text();
                original.text('تم النسخ!').css('background-color', '#46b450');
                setTimeout(function() {
                    original.text(originalText).css('background-color', '');
                }, 1000);
            }.bind(this));
        }
    });
    
    // إضافة وظائف إضافية لتحسين تجربة المستخدم
    
    // إضافة أزرار إدراج العناصر النائبة بسرعة
    function addPlaceholderButtons() {        var purchasePlaceholders = [
            '{order_number}', '{customer_name}', '{site_name}', '{license_keys}'
        ];
        var expiryPlaceholders = [
            '{license_key}', '{expiry_date}', '{customer_name}', '{site_name}'
        ];
        
        // إضافة أزرار للمحتوى
        if ($('#purchase_email_content_ifr').length) {
            addQuickInsertButtons('purchase', purchasePlaceholders);
        }
        if ($('#expiry_email_content_ifr').length) {
            addQuickInsertButtons('expiry', expiryPlaceholders);
        }
    }
    
    function addQuickInsertButtons(type, placeholders) {
        var container = $('#' + type + '_email_content').closest('td');
        var buttonGroup = $('<div class="placeholder-buttons" style="margin-top: 10px;"></div>');
        
        placeholders.forEach(function(placeholder) {
            var button = $('<button type="button" class="button button-small" style="margin-right: 5px; margin-bottom: 5px;">' + placeholder + '</button>');
            button.on('click', function(e) {
                e.preventDefault();
                insertPlaceholder(type + '_email_content', placeholder);
            });
            buttonGroup.append(button);
        });
        
        container.append('<p style="margin-top: 10px; margin-bottom: 5px; font-weight: bold;">إدراج سريع:</p>');
        container.append(buttonGroup);
    }
    
    function insertPlaceholder(editorId, placeholder) {
        if (tinyMCE.get(editorId)) {
            tinyMCE.get(editorId).insertContent(placeholder);
            updatePreview(editorId.includes('purchase') ? 'purchase' : 'expiry');
        } else {
            var editor = $('#' + editorId);
            var cursorPos = editor[0].selectionStart;
            var textBefore = editor.val().substring(0, cursorPos);
            var textAfter = editor.val().substring(cursorPos);
            editor.val(textBefore + placeholder + textAfter);
            editor[0].selectionStart = editor[0].selectionEnd = cursorPos + placeholder.length;
            updatePreview(editorId.includes('purchase') ? 'purchase' : 'expiry');
        }
    }
    
    // تشغيل إضافة الأزرار بعد تحميل المحررات
    setTimeout(addPlaceholderButtons, 1000);
    
    // إضافة مؤشر حفظ تلقائي
    var saveIndicator = $('<div id="save-indicator" style="position: fixed; top: 32px; right: 20px; background: #46b450; color: white; padding: 10px; border-radius: 4px; display: none; z-index: 9999;">✓ تم الحفظ</div>');
    $('body').append(saveIndicator);
    
    // إظهار مؤشر الحفظ عند نجاح العملية
    if ($('.notice-success').length > 0) {
        saveIndicator.fadeIn().delay(3000).fadeOut();
    }
    
    showTooltips();
});
