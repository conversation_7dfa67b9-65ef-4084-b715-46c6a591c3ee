<?php
/**
 * Scheduled Tasks (WP Cron) for RIDCODTOKEN
 *
 * @package RIDCODTOKEN
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

// Define the custom cron hook name
define( 'RIDCODTOKEN_CRON_HOOK', 'ridcodtoken_daily_license_check' );

/**
 * The main function executed by the scheduled event.
 * Checks for licenses expiring soon and sends notifications.
 */
function ridcodtoken_check_expiring_licenses() {
	global $wpdb;
	$table_licenses = $wpdb->prefix . 'ridcod_licenses';

	// Get email settings
	$email_settings = get_option('ridcodtoken_email_settings', array(
		'expiry_email_enabled' => 1,
		'expiry_email_subject' => __('Your License Key is Expiring Soon', 'ridcodtoken'),
		'expiry_email_content' => __('<p>Hello,</p><p>This is a friendly reminder that your license key <strong>{license_key}</strong> is set to expire on {expiry_date}.</p><p>Please renew your license to continue receiving updates and support.</p><p>Thank you,<br>{site_name}</p>', 'ridcodtoken'),
		'expiry_email_days' => 7,
	));

	// Check if expiry email is enabled
	if (empty($email_settings['expiry_email_enabled'])) {
		return;
	}

	// Calculate dates based on settings
	$current_time_gmt = current_time( 'mysql', 1 ); // Get current GMT time
	$days_before_expiry = absint($email_settings['expiry_email_days']);
    $expiry_check_date = date( 'Y-m-d H:i:s', strtotime( '+' . $days_before_expiry . ' days', strtotime( $current_time_gmt ) ) );

	// Find active licenses expiring within the configured days
	$expiring_licenses = $wpdb->get_results( $wpdb->prepare(
		"SELECT id, license_key, user_email, expires_at
		 FROM $table_licenses
		 WHERE status = 'active'
		   AND expires_at IS NOT NULL
		   AND expires_at > %s
		   AND expires_at <= %s",
		$current_time_gmt,
        $expiry_check_date
	) );

	if ( empty( $expiring_licenses ) ) {
		// Optional: Log that the check ran but found nothing
		// error_log("RIDCODTOKEN Cron: No licenses expiring within " . $days_before_expiry . " days found on " . $current_time_gmt);
		return;
	}

	// Send email for each expiring license
	foreach ( $expiring_licenses as $license ) {
		if ( ! empty( $license->user_email ) && is_email( $license->user_email ) ) {
			$to = $license->user_email;
            $expiry_date = date_i18n( get_option( 'date_format' ), strtotime( $license->expires_at ) ); // Format date nicely
			$site_name = get_bloginfo('name');
			$customer_name = ''; // Could be enhanced to get customer name if needed

			// Replace placeholders in subject
			$subject = str_replace(
				array('{license_key}', '{expiry_date}', '{customer_name}', '{site_name}'),
				array($license->license_key, $expiry_date, $customer_name, $site_name),
				$email_settings['expiry_email_subject']
			);

			// Replace placeholders in content
			$body = str_replace(
				array('{license_key}', '{expiry_date}', '{customer_name}', '{site_name}'),
				array($license->license_key, $expiry_date, $customer_name, $site_name),
				$email_settings['expiry_email_content']
			);

			$headers = array('Content-Type: text/html; charset=UTF-8');

			wp_mail( $to, $subject, $body, $headers );

            // Optional: Log email sending
            // error_log("RIDCODTOKEN Cron: Sent expiry notification to " . $to . " for license ID " . $license->id);
		} else {
            // Optional: Log if email is missing or invalid
            // error_log("RIDCODTOKEN Cron: Could not send expiry notification for license ID " . $license->id . " - missing or invalid email.");
        }
	}
}
// Hook the function to our custom cron schedule
add_action( RIDCODTOKEN_CRON_HOOK, 'ridcodtoken_check_expiring_licenses' );