/**
 * RIDCODTOKEN Plugin SDK - Admin Styles
 * تنسيقات واجهة إدارة التراخيص
 */

/* تنسيق عام للصفحات */
.ridcodtoken-sdk-page {
    max-width: 1200px;
}

/* تنسيق البطاقات */
.ridcodtoken-sdk-page .card {
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e1e1e1;
    background: #fff;
}

.ridcodtoken-sdk-page .card h2,
.ridcodtoken-sdk-page .card h3 {
    margin-top: 0;
    color: #23282d;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
}

/* تنسيق النموذج */
#ridcodtoken-activation-form {
    margin: 0;
}

#ridcodtoken-activation-form .form-table {
    margin-bottom: 20px;
}

#ridcodtoken-activation-form .form-table th {
    width: 150px;
    font-weight: 600;
}

#ridcodtoken-activation-form .regular-text {
    width: 100%;
    max-width: 400px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

#ridcodtoken-activation-form .regular-text:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

/* تنسيق الأزرار */
.ridcodtoken-sdk-page .button {
    position: relative;
    transition: all 0.2s ease;
}

.ridcodtoken-sdk-page .button .spinner {
    float: none;
    margin: 0 5px 0 0;
    vertical-align: middle;
}

.ridcodtoken-sdk-page .button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.ridcodtoken-sdk-page .button-primary {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
    font-weight: 600;
    padding: 10px 20px;
    height: auto;
}

.ridcodtoken-sdk-page .button-primary:hover:not(:disabled) {
    background: #005a87;
    border-color: #005a87;
}

.ridcodtoken-sdk-page .button-secondary {
    background: #f7f7f7;
    border-color: #ccc;
    color: #555;
    font-weight: 600;
    padding: 8px 16px;
    height: auto;
}

.ridcodtoken-sdk-page .button-secondary:hover:not(:disabled) {
    background: #fafafa;
    border-color: #999;
}

.ridcodtoken-sdk-page .button-link-delete {
    color: #a00;
    text-decoration: none;
    font-weight: 600;
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: 3px;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
}

.ridcodtoken-sdk-page .button-link-delete:hover {
    color: #fff;
    background: #d63638;
    border-color: #d63638;
    text-decoration: none;
}

/* تنسيق الرسائل */
#license-message {
    margin-top: 20px;
}

#license-message .notice {
    margin: 0;
    padding: 12px;
    border-left: 4px solid;
    background: #fff;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

#license-message .notice-success {
    border-left-color: #00a32a;
    background: #f0f8f0;
}

#license-message .notice-error {
    border-left-color: #d63638;
    background: #fef7f7;
}

#license-message .notice-warning {
    border-left-color: #dba617;
    background: #fcf9e8;
}

#license-message .notice-info {
    border-left-color: #72aee6;
    background: #f0f6fc;
}

/* تنسيق جدول المعلومات */
.ridcodtoken-sdk-page .form-table {
    background: #fafafa;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
}

.ridcodtoken-sdk-page .form-table th {
    background: #f1f1f1;
    font-weight: 600;
    color: #23282d;
    padding: 15px 20px;
    border-bottom: 1px solid #e1e1e1;
}

.ridcodtoken-sdk-page .form-table td {
    padding: 15px 20px;
    border-bottom: 1px solid #e1e1e1;
    vertical-align: middle;
}

.ridcodtoken-sdk-page .form-table tr:last-child th,
.ridcodtoken-sdk-page .form-table tr:last-child td {
    border-bottom: none;
}

.ridcodtoken-sdk-page .form-table code {
    background: #f1f1f1;
    padding: 4px 8px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #d63638;
}

/* تنسيق حالة الترخيص */
.license-status-active {
    color: #00a32a;
    font-weight: bold;
}

.license-status-inactive {
    color: #d63638;
    font-weight: bold;
}

.license-status-expired {
    color: #dba617;
    font-weight: bold;
}

/* تنسيق التنبيهات */
.ridcodtoken-sdk-page .notice {
    margin: 20px 0;
    padding: 12px;
    border-left: 4px solid;
    background: #fff;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.ridcodtoken-sdk-page .notice p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}

.ridcodtoken-sdk-page .notice-warning {
    border-left-color: #dba617;
    background: #fcf9e8;
}

.ridcodtoken-sdk-page .notice-success {
    border-left-color: #00a32a;
    background: #f0f8f0;
}

/* تنسيق responsive */
@media screen and (max-width: 782px) {
    .ridcodtoken-sdk-page .card {
        padding: 15px;
    }
    
    .ridcodtoken-sdk-page .form-table th,
    .ridcodtoken-sdk-page .form-table td {
        padding: 10px 15px;
        display: block;
        width: 100%;
    }
    
    .ridcodtoken-sdk-page .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
    
    .ridcodtoken-sdk-page .form-table td {
        padding-top: 5px;
    }
    
    #ridcodtoken-activation-form .regular-text {
        max-width: 100%;
    }
    
    .ridcodtoken-sdk-page .button {
        width: 100%;
        text-align: center;
        margin-bottom: 10px;
    }
}

/* تحسينات إضافية */
.ridcodtoken-sdk-page .description {
    color: #666;
    font-style: italic;
    margin-top: 5px;
}

.ridcodtoken-sdk-page .submit {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e1e1e1;
}

/* تأثيرات التحميل */
.ridcodtoken-sdk-page .spinner.is-active {
    display: inline-block;
    visibility: visible;
}

/* تنسيق خاص للأيقونات */
.ridcodtoken-sdk-page .dashicons {
    vertical-align: middle;
    margin-right: 5px;
}

/* تحسين مظهر الكود */
.ridcodtoken-sdk-page pre {
    background: #f1f1f1;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 13px;
    line-height: 1.4;
}
