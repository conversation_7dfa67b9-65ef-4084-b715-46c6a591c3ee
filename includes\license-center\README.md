# مركز التراخيص الموحد - RIDCODTOKEN License Center

## 🎯 نظرة عامة

مركز التراخيص الموحد هو حل شامل لإدارة جميع تراخيص إضافات RIDCODTOKEN من مكان واحد، بدلاً من الحاجة لصفحة منفصلة لكل إضافة.

---

## ✨ المميزات

### 🏠 إدارة مركزية
- **صفحة واحدة** لجميع التراخيص
- **نظرة شاملة** على حالة جميع الإضافات
- **إحصائيات فورية** للتراخيص النشطة وغير النشطة

### ⚡ تفعيل سريع
- **تفعيل بنقرة واحدة** من الصفحة الرئيسية
- **كشف تلقائي** للإضافة المناسبة للترخيص
- **تفعيل مجمع** لعدة تراخيص

### 🔍 مراقبة ذكية
- **فحص دوري** لجميع التراخيص
- **إشعارات موحدة** بدلاً من إشعارات متعددة
- **تحديث تلقائي** لحالة التراخيص

### 🎨 واجهة محسنة
- **تصميم عصري** وسهل الاستخدام
- **استجابة كاملة** للشاشات المختلفة
- **تفاعل سلس** مع AJAX

---

## 🏗️ البنية التقنية

### الملفات الأساسية
```
includes/license-center/
├── unified-center.php      # الفئة الرئيسية
├── center-page.php         # واجهة المستخدم
├── stats-page.php          # صفحة الإحصائيات
└── README.md              # هذا الملف

assets/
├── css/license-center.css  # أنماط CSS
└── js/license-center.js    # JavaScript
```

### الفئات والدوال
- `RIDCODTOKEN_Unified_License_Center` - الفئة الرئيسية
- `detect_ridcodtoken_plugins()` - كشف الإضافات
- `activate_license_for_plugin()` - تفعيل الترخيص
- `bulk_license_check()` - فحص مجمع

---

## 🚀 كيفية العمل

### 1. الكشف التلقائي
```php
// البحث عن جميع الإضافات التي تحتوي على SDK
foreach (get_plugins() as $plugin_file => $plugin_data) {
    $plugin_dir = dirname(WP_PLUGIN_DIR . '/' . $plugin_file);
    
    // البحث عن ملف SDK
    if (file_exists($plugin_dir . '/sdk/ridcodtoken-plugin-sdk.php')) {
        // إضافة الإضافة للقائمة
    }
}
```

### 2. التكامل مع SDK
```php
// في SDK الخاص بكل إضافة
private function unified_center_exists() {
    return class_exists('RIDCODTOKEN_Unified_License_Center');
}

private function register_with_unified_center() {
    add_filter('ridcodtoken_unified_plugins', function($plugins) {
        $plugins[] = array(
            'slug' => $this->plugin_info['plugin_slug'],
            'name' => $this->plugin_info['plugin_name'],
            'product_id' => $this->plugin_info['product_id'],
            // ...
        );
        return $plugins;
    });
}
```

### 3. تفعيل الترخيص
```php
// تفعيل ترخيص لإضافة محددة
function activate_license_for_plugin($license_key, $plugin_slug, $product_id) {
    // التحقق من صحة الترخيص مع الخادم
    $api_response = $this->verify_license_with_server($license_key, $product_id);
    
    if ($api_response['success']) {
        // حفظ الترخيص للإضافة المحددة
        $prefix = 'ridcodtoken_' . sanitize_key($plugin_slug) . '_';
        update_option($prefix . 'license_key', $license_key);
        update_option($prefix . 'license_status', 'active');
    }
}
```

---

## 📋 دليل الاستخدام

### للمستخدمين النهائيين

#### الوصول للمركز
1. اذهب إلى لوحة الإدارة
2. ابحث عن **"License Center"** في القائمة الجانبية
3. انقر عليها للوصول للمركز الموحد

#### تفعيل ترخيص جديد
1. **الطريقة السريعة:**
   - أدخل مفتاح الترخيص في حقل "Quick License Activation"
   - اختر الإضافة المناسبة (أو اتركها فارغة للكشف التلقائي)
   - انقر "Activate License"

2. **من بطاقة الإضافة:**
   - ابحث عن الإضافة في قائمة "Installed RIDCODTOKEN Plugins"
   - انقر "Activate License" على الإضافة المطلوبة
   - أدخل مفتاح الترخيص في النافذة المنبثقة

#### إدارة التراخيص الموجودة
- **فحص الترخيص:** انقر "Check License" لفحص ترخيص واحد
- **فحص جميع التراخيص:** انقر "Check All" في أعلى الصفحة
- **إلغاء التفعيل:** انقر "Deactivate" لإلغاء تفعيل ترخيص

### للمطورين

#### إضافة دعم المركز الموحد لإضافة جديدة
```php
// في SDK الخاص بالإضافة
private function init() {
    // التحقق من وجود المركز الموحد
    if ($this->unified_center_exists()) {
        // تسجيل الإضافة مع المركز الموحد
        $this->register_with_unified_center();
        
        // إضافة قائمة بسيطة للوصول السريع فقط
        if (!$this->is_license_valid()) {
            add_action('admin_menu', array($this, 'add_quick_access_menu'));
        }
    } else {
        // إضافة القائمة التقليدية
        add_action('admin_menu', array($this, 'add_license_menu'));
    }
}
```

#### إضافة معرف منتج جديد
```php
// في unified-center.php
private function guess_product_id($plugin_slug) {
    $known_products = array(
        'ridcod-shorts' => 222,
        'ridcod-seo' => 333,
        'your-new-plugin' => 444, // أضف هنا
    );
    
    return isset($known_products[$plugin_slug]) ? $known_products[$plugin_slug] : 0;
}
```

---

## 🔧 التخصيص والتطوير

### إضافة ميزات جديدة

#### إضافة صفحة فرعية جديدة
```php
add_submenu_page(
    'ridcodtoken-center',
    'Page Title',
    'Menu Title',
    'manage_options',
    'ridcodtoken-center-custom',
    array($this, 'render_custom_page')
);
```

#### إضافة معالج AJAX جديد
```php
add_action('wp_ajax_ridcodtoken_custom_action', array($this, 'ajax_custom_action'));

public function ajax_custom_action() {
    check_ajax_referer('ridcodtoken_center_nonce', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => 'ليس لديك صلاحية'));
    }
    
    // منطق العملية هنا
    
    wp_send_json_success($result);
}
```

### تخصيص التصميم

#### تعديل الألوان
```css
/* في license-center.css */
.stat-box.custom {
    border-left: 4px solid #your-color;
}

.plugin-card.custom {
    border-left: 4px solid #your-color;
}
```

#### إضافة أنماط جديدة
```css
.ridcodtoken-custom-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}
```

---

## 🧪 الاختبار

### اختبار الوظائف الأساسية
1. **كشف الإضافات:** تأكد من ظهور جميع إضافات RIDCODTOKEN
2. **تفعيل الترخيص:** اختبر تفعيل ترخيص صحيح
3. **رفض الترخيص:** اختبر رفض ترخيص خاطئ
4. **فحص التراخيص:** اختبر فحص التراخيص الموجودة

### اختبار التكامل
1. **مع SDK:** تأكد من عدم ظهور قوائم منفصلة عند وجود المركز
2. **مع WooCommerce:** اختبر إنشاء التراخيص التلقائي
3. **مع REST API:** تأكد من عمل جميع endpoints

### اختبار الأداء
1. **مع إضافات متعددة:** اختبر مع 5+ إضافات
2. **استجابة AJAX:** تأكد من سرعة الاستجابة
3. **استهلاك الذاكرة:** راقب استهلاك الموارد

---

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### "No RIDCODTOKEN plugins found"
**السبب:** لا توجد إضافات تحتوي على SDK  
**الحل:** تأكد من وجود ملف SDK في مجلد الإضافة

#### "License activation failed"
**السبب:** مشكلة في الاتصال بالخادم أو ترخيص غير صحيح  
**الحل:** 
- تحقق من الاتصال بالإنترنت
- تأكد من صحة مفتاح الترخيص
- تحقق من معرف المنتج

#### "Plugin not detected automatically"
**السبب:** معرف المنتج غير مُعرف في النظام  
**الحل:** أضف معرف المنتج في دالة `guess_product_id()`

#### "Unified center not working"
**السبب:** لم يتم تحميل الفئة الرئيسية  
**الحل:** تأكد من تحميل `unified-center.php` في `ridcodtoken.php`

### تسجيل الأخطاء
```php
// تفعيل تسجيل الأخطاء
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);

// في الكود
error_log('RIDCODTOKEN Center: ' . $error_message);
```

---

## 📊 الإحصائيات والمراقبة

### البيانات المتاحة
- عدد الإضافات المثبتة
- عدد التراخيص النشطة
- عدد التراخيص المنتهية الصلاحية
- آخر فحص للتراخيص

### تصدير البيانات
```php
// الحصول على تقرير شامل
$report = array(
    'total_plugins' => count($this->detected_plugins),
    'active_licenses' => count(array_filter($plugins, function($p) { 
        return $p['license_status'] === 'active'; 
    })),
    'last_check' => time(),
    'plugins' => $this->detected_plugins
);
```

---

## 🔮 التطوير المستقبلي

### ميزات مخططة
- [ ] **تفعيل مجمع:** تفعيل عدة تراخيص دفعة واحدة
- [ ] **تصدير/استيراد:** نسخ احتياطي للتراخيص
- [ ] **إشعارات متقدمة:** تنبيهات انتهاء الصلاحية
- [ ] **تقارير مفصلة:** إحصائيات الاستخدام
- [ ] **API خارجي:** إدارة التراخيص من خارج WordPress

### تحسينات مقترحة
- [ ] **ذاكرة تخزين مؤقت:** تحسين الأداء
- [ ] **بحث وفلترة:** في قائمة الإضافات
- [ ] **تجميع حسب المطور:** تنظيم أفضل
- [ ] **وضع الصيانة:** إيقاف مؤقت للتحقق

---

## 📞 الدعم والمساهمة

### الحصول على المساعدة
- **الوثائق:** راجع هذا الملف أولاً
- **الأخطاء:** تحقق من سجل WordPress
- **الدعم:** تواصل مع فريق RIDCODTOKEN

### المساهمة في التطوير
- **تقارير الأخطاء:** أرسل تفاصيل المشكلة
- **اقتراحات الميزات:** شارك أفكارك
- **تحسينات الكود:** قدم pull requests

---

## 📄 الترخيص

هذا المركز جزء من نظام RIDCODTOKEN ومرخص تحت نفس شروط النظام الأساسي.

---

**تم إنشاؤه بواسطة فريق RIDCODTOKEN** 🚀
