<?php
/**
 * Elementor Integration for RIDCODTOKEN
 *
 * @package RIDCODTOKEN
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Register RIDCODTOKEN Elementor Widgets.
 *
 * @param \Elementor\Widgets_Manager $widgets_manager Elementor widgets manager.
 * @return void
 */
function ridcodtoken_register_elementor_widgets( $widgets_manager ) {
	// Include the widget file
	require_once RIDCODTOKEN_PLUGIN_DIR . 'includes/elementor/widgets/class-ridcodtoken-activate-widget.php';

	// Register the widget
	$widgets_manager->register_widget_type( new \RIDCODTOKEN\Elementor\Widgets\Activate_Widget() );
}

/**
 * Check if Elementor is loaded and then register widgets.
 */
function ridcodtoken_init_elementor_widgets() {
	// Check if Elementor is installed and active
	if ( did_action( 'elementor/loaded' ) ) {
		add_action( 'elementor/widgets/widgets_registered', 'ridcodtoken_register_elementor_widgets' );

        // You might also register widget categories here if needed
        // add_action( 'elementor/elements/categories_registered', 'ridcodtoken_add_elementor_widget_categories' );

	} else {
        // Optional: Add admin notice if Elementor is required but not active
        // add_action( 'admin_notices', 'ridcodtoken_fail_load_elementor_notice' );
    }
}
add_action( 'init', 'ridcodtoken_init_elementor_widgets' );

/* Optional: Add custom category
function ridcodtoken_add_elementor_widget_categories( $elements_manager ) {
	$elements_manager->add_category(
		'ridcodtoken',
		[
			'title' => __( 'RIDCODTOKEN', 'ridcodtoken' ),
			'icon' => 'fa fa-plug',
		]
	);
}
*/

/**
 * AJAX handler to save the activated license key.
 */
function ridcodtoken_ajax_save_license_key() {
    // 1. Verify Nonce
    // Use the nonce action name we defined in wp_localize_script and added as a hidden field
    check_ajax_referer( 'ridcodtoken_save_key_nonce', '_ajax_nonce' );

    // 2. Check User Permissions (Optional but recommended)
    // Example: Ensure only logged-in users or admins can save?
    // if ( ! current_user_can( 'manage_options' ) ) {
    //     wp_send_json_error( array( 'message' => __( 'Permission denied.', 'ridcodtoken' ) ), 403 );
    // }

    // 3. Get and Sanitize License Key
    if ( ! isset( $_POST['license_key'] ) || empty( trim( $_POST['license_key'] ) ) ) {
         wp_send_json_error( array( 'message' => __( 'Invalid license key provided.', 'ridcodtoken' ) ), 400 );
    }
    $license_key = sanitize_text_field( $_POST['license_key'] );

    // 4. Basic Validation (Optional - REST API already validated, but good practice)
    // Example: Check length or format if applicable
    // if ( strlen( $license_key ) !== 36 ) { // Example: UUID length check
    //     wp_send_json_error( array( 'message' => __( 'Invalid license key format.', 'ridcodtoken' ) ), 400 );
    // }

    // 5. Save the license key and status
    // It's crucial that this AJAX handler is only called *after* the REST API
    // successfully activated the key. The JS ensures this.
    update_option( 'ridcodtoken_activated_key', $license_key );
    update_option( 'ridcodtoken_license_status', 'active' ); // Mark as active locally

    // 6. Send Success Response
    wp_send_json_success( array( 'message' => __( 'License key saved successfully!', 'ridcodtoken' ) ) );
}
// Hook for logged-in users
add_action( 'wp_ajax_ridcodtoken_save_license', 'ridcodtoken_ajax_save_license_key' );
// Hook for non-logged-in users (if the widget can be used by them - consider security implications)
// add_action( 'wp_ajax_nopriv_ridcodtoken_save_license', 'ridcodtoken_ajax_save_license_key' );


/* Optional: Admin notice if Elementor not active
function ridcodtoken_fail_load_elementor_notice() {
	// Check if the notice has been dismissed
	// if ( ! get_user_meta( get_current_user_id(), 'dismissed_ridcodtoken_elementor_notice', true ) ) {
		$message = sprintf(
			esc_html__( '"%1$s" requires "%2$s" to be installed and activated. Please install %2$s to use the RIDCODTOKEN Elementor widgets.', 'ridcodtoken' ),
			'<strong>RIDCODTOKEN Plugin</strong>',
			'<strong>Elementor</strong>'
		);
		printf( '<div class="notice notice-warning is-dismissible ridcodtoken-elementor-notice"><p>%1$s</p></div>', $message );
		// Add script to handle dismissal via AJAX if needed
	// }
}
*/