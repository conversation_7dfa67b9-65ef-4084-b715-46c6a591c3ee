# RIDCODTOKEN Plugin SDK - دليل التطبيق

## نظرة عامة

هذا SDK يسمح لك بحماية أي إضافة WordPress بنظام التراخيص RIDCODTOKEN. عند تطبيق هذا SDK على إضافة، ستصبح الإضافة تتطلب ترخيص صحيح للعمل.

## المميزات

- ✅ **حماية كاملة للإضافة** - لا تعمل الإضافة بدون ترخيص صحيح
- ✅ **إخفاء الإضافة** - تختفي من قائمة الإضافات عند عدم وجود ترخيص
- ✅ **التحقق الدوري** - فحص الترخيص كل 5 ساعات تلقائياً
- ✅ **واجهة سهلة** - صفحة بسيطة لإدخال وإدارة الترخيص
- ✅ **API متكامل** - يعمل مع نظام RIDCODTOKEN الموجود
- ✅ **أمان عالي** - تشفير وحماية البيانات

## متطلبات التشغيل

- WordPress 5.0 أو أحدث
- PHP 7.2 أو أحدث
- نظام RIDCODTOKEN مثبت ومفعل على الخادم الرئيسي

## طريقة التطبيق

### الخطوة 1: نسخ ملفات SDK

1. انسخ مجلد `plugin-sdk` بالكامل إلى مجلد الإضافة التي تريد حمايتها
2. تأكد من أن المجلد يحتوي على الملفات التالية:
   ```
   plugin-sdk/
   ├── ridcodtoken-plugin-sdk.php
   └── assets/
       ├── admin.js
       └── admin.css
   ```

### الخطوة 2: تعديل الملف الرئيسي للإضافة

أضف الكود التالي في بداية الملف الرئيسي للإضافة (بعد التحقق من `ABSPATH`):

```php
<?php
// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// تحميل SDK الحماية
require_once plugin_dir_path(__FILE__) . 'plugin-sdk/ridcodtoken-plugin-sdk.php';

// إعداد SDK
$license_sdk = ridcodtoken_init_plugin_sdk(array(
    'plugin_file' => __FILE__,                    // مسار الملف الرئيسي للإضافة
    'plugin_name' => 'اسم الإضافة',               // اسم الإضافة للعرض
    'api_url' => 'https://yoursite.com',          // رابط موقع RIDCODTOKEN
    'plugin_slug' => 'my-plugin',                 // معرف فريد للإضافة
    'version' => '1.0.0'                          // إصدار الإضافة
));

// التحقق من صحة الترخيص قبل تحميل وظائف الإضافة
if (!$license_sdk->is_licensed()) {
    // إذا لم يكن الترخيص صحيح، لا تحمل باقي الإضافة
    return;
}

// هنا يمكنك وضع باقي كود الإضافة
// سيتم تنفيذ هذا الجزء فقط إذا كان الترخيص صحيح

// مثال:
class My_Protected_Plugin {
    public function __construct() {
        add_action('init', array($this, 'init'));
    }
    
    public function init() {
        // وظائف الإضافة هنا
    }
}

new My_Protected_Plugin();
?>
```

### الخطوة 3: تخصيص الإعدادات

قم بتعديل المعاملات في دالة `ridcodtoken_init_plugin_sdk()`:

#### المعاملات المطلوبة:

- **`plugin_file`**: مسار الملف الرئيسي للإضافة (استخدم `__FILE__`)
- **`plugin_name`**: اسم الإضافة كما تريد عرضه للمستخدم
- **`api_url`**: رابط الموقع الذي يحتوي على نظام RIDCODTOKEN
- **`plugin_slug`**: معرف فريد للإضافة (استخدم أحرف وأرقام وشرطات فقط)
- **`version`**: إصدار الإضافة

#### مثال كامل:

```php
$license_sdk = ridcodtoken_init_plugin_sdk(array(
    'plugin_file' => __FILE__,
    'plugin_name' => 'إضافة التجارة الإلكترونية المتقدمة',
    'api_url' => 'https://mystore.com',
    'plugin_slug' => 'advanced-ecommerce',
    'version' => '2.1.0'
));
```

### الخطوة 4: اختبار التطبيق

1. ارفع الإضافة المحدثة إلى موقع WordPress
2. فعل الإضافة من لوحة الإدارة
3. ستلاحظ أن الإضافة اختفت من قائمة الإضافات
4. ستجد قائمة جديدة باسم الإضافة في القائمة الجانبية
5. ادخل إلى القائمة وأدخل مفتاح ترخيص صحيح
6. بعد التفعيل، ستعمل الإضافة بشكل طبيعي

## كيفية عمل النظام

### عند عدم وجود ترخيص:
- الإضافة تختفي من قائمة الإضافات
- تظهر قائمة واحدة فقط لتفعيل الترخيص
- لا يتم تحميل أي وظائف من الإضافة
- عرض رسالة تنبيه في لوحة الإدارة

### عند وجود ترخيص صحيح:
- الإضافة تعمل بشكل طبيعي
- التحقق من الترخيص كل 5 ساعات تلقائياً
- إضافة قائمة فرعية لإدارة الترخيص
- إمكانية إلغاء تفعيل الترخيص

### التحقق الدوري:
- يتم فحص الترخيص كل 5 ساعات
- في حالة انتهاء الترخيص، يتم تعطيل الإضافة فوراً
- في حالة فشل الاتصال، يتم إعطاء مهلة ساعة واحدة

## الواجهات المتاحة

### صفحة تفعيل الترخيص
- نموذج بسيط لإدخال مفتاح الترخيص
- رسائل واضحة للأخطاء والنجاح
- معلومات الإضافة والحالة

### صفحة إدارة الترخيص
- عرض معلومات الترخيص الحالي
- إمكانية فحص الترخيص يدوياً
- إمكانية إلغاء تفعيل الترخيص
- عرض آخر تحقق من الترخيص

## استكشاف الأخطاء

### الإضافة لا تظهر بعد التفعيل
- تأكد من صحة مفتاح الترخيص
- تحقق من رابط API في الإعدادات
- تأكد من أن الموقع الرئيسي يعمل بشكل صحيح

### رسالة "فشل في الاتصال"
- تحقق من اتصال الإنترنت
- تأكد من أن رابط API صحيح
- تحقق من إعدادات الجدار الناري

### الإضافة تتوقف عن العمل فجأة
- قد يكون الترخيص انتهى
- تحقق من صفحة إدارة الترخيص
- تواصل مع مزود الترخيص

## الأمان

- جميع البيانات محمية ومشفرة
- التحقق من الصلاحيات قبل أي عملية
- حماية من هجمات CSRF
- تنظيف جميع المدخلات

## الدعم الفني

إذا واجهت أي مشاكل في التطبيق:

1. تأكد من اتباع جميع الخطوات بدقة
2. تحقق من سجل الأخطاء في WordPress
3. تواصل مع فريق الدعم مع تفاصيل المشكلة

## ملاحظات مهمة

- ⚠️ **لا تحذف مجلد `plugin-sdk`** من الإضافة
- ⚠️ **احتفظ بنسخة احتياطية** من الإضافة قبل التعديل
- ⚠️ **اختبر النظام** على موقع تجريبي أولاً
- ⚠️ **تأكد من صحة رابط API** قبل النشر

## التحديثات

عند تحديث الإضافة:
- احتفظ بمجلد `plugin-sdk` كما هو
- لا تغير إعدادات SDK إلا إذا لزم الأمر
- اختبر النظام بعد كل تحديث

---

**تم تطوير هذا SDK بواسطة فريق RIDCODTOKEN**
