<?php
/**
 * WooCommerce Integration for RIDCODTOKEN License Generation
 *
 * @package RIDCODTOKEN
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Generates and assigns a license key when a relevant product order is completed.
 *
 * @param int $order_id The ID of the completed order.
 */
function ridcodtoken_generate_license_on_order_completion( $order_id ) {
	global $wpdb;
	$order = wc_get_order( $order_id );

	// Ensure it's a valid WC_Order object
	if ( ! $order ) {
		error_log("RIDCODTOKEN: Could not retrieve order object for order ID: " . $order_id);
		return;
	}

	$customer_email = $order->get_billing_email();
	if ( ! $customer_email ) {
		error_log("RIDCODTOKEN: Order ID " . $order_id . " has no billing email. Cannot generate/send license.");
		return;
	}

	// Get license plans from database instead of hardcoded settings
	$table_plans = $wpdb->prefix . 'ridcod_license_plans';
	$active_plans = $wpdb->get_results("SELECT * FROM $table_plans WHERE status = 'active'", OBJECT_K);

	// Check if any plans are configured
	if ( empty( $active_plans ) ) {
		// Optional: Log info - no plans configured for licensing
		error_log("RIDCODTOKEN: No active license plans configured. Skipping license generation for order ID: " . $order_id);
		return;
	}
	$table_licenses = $wpdb->prefix . 'ridcod_licenses';
	$licenses_generated = array(); // To store generated keys for emailing

	// Loop through order items
	foreach ( $order->get_items() as $item_id => $item ) {
		$product_id = $item->get_product_id(); // Get product ID from the item
		$product = $item->get_product(); // Get product object for name, etc.
		
		// Check if the purchased product ID matches any of the configured license plans
		$plan = null;
		foreach ($active_plans as $active_plan) {
			if ($product_id && $product_id == $active_plan->product_id) {
				$plan = $active_plan;
				break;
			}
		}

		// If this product matches a license plan, generate a key
		if ( $plan && $product ) {
			$sites_allowed = $plan->sites_allowed;

			// Generate a unique license key
			$license_key = ridcodtoken_create_unique_license_key();
			if ( ! $license_key ) {
				// Optional: Log error - failed to generate unique key after retries
				error_log("RIDCODTOKEN: Failed to generate a unique license key for order ID " . $order_id . ", product ID " . $product_id);
				continue; // Skip to next item
			}

			// Calculate expiration date based on plan duration
			$expires_at = null;
			if ($plan->duration_days && $plan->duration_days > 0) {
				$expires_at = date('Y-m-d H:i:s', strtotime('+' . $plan->duration_days . ' days'));
			}

			// Prepare data for insertion
			$license_data = array(
				'license_key'   => $license_key,
				'user_email'    => $customer_email,
				'status'        => 'active', // License is active upon creation
				'sites_allowed' => $sites_allowed,
				'order_id'      => $order_id,
				'product_id'    => $product_id,
				'created_at'    => current_time( 'mysql', 1 ), // GMT time
				'expires_at'    => $expires_at,
			);

			// Insert into the database
			$inserted = $wpdb->insert( $table_licenses, $license_data, array( '%s', '%s', '%s', '%d', '%d', '%d', '%s', '%s' ) );

			if ( $inserted ) {
				// Store for emailing later
				$licenses_generated[] = array(
					'key' => $license_key,
					'product_name' => $product->get_name(),
				);
				// Optional: Add order note
				$order->add_order_note( sprintf( __( 'RIDCODTOKEN: Generated license key %s for product %s.', 'ridcodtoken' ), $license_key, $product->get_name() ) );
			} else {
				// Optional: Log error
				error_log("RIDCODTOKEN: Failed to insert license key into database for order ID " . $order_id . ", product ID " . $product_id);
				// Optional: Add order note about failure
				$order->add_order_note( sprintf( __( 'RIDCODTOKEN: Failed to generate license key for product %s.', 'ridcodtoken' ), $product->get_name() ), true ); // Make it a customer note?
			}
		}
	}

	// Send email if any licenses were generated
	if ( ! empty( $licenses_generated ) ) {
		ridcodtoken_send_license_email( $customer_email, $licenses_generated, $order );
	}
}
// Hook into WooCommerce order completion
add_action( 'woocommerce_order_status_completed', 'ridcodtoken_generate_license_on_order_completion', 10, 1 );

// Note: ridcodtoken_create_unique_license_key() function moved to includes/utils.php

/**
 * Sends the license key email to the customer.
 *
 * @param string $to_email The customer's email address.
 * @param array  $licenses Array of generated license details (key, product_name).
 * @param WC_Order $order The order object.
 */
function ridcodtoken_send_license_email( $to_email, $licenses, $order ) {
	// Get email settings
	$email_settings = get_option('ridcodtoken_email_settings', array(
		'purchase_email_enabled' => 1,
		'purchase_email_subject' => __('Your License Key(s) for Order #{order_number}', 'ridcodtoken'),
		'purchase_email_content' => __('<p>Thank you for your purchase!</p><p>Here are your license key(s):</p>{license_keys}<p>Please keep these keys safe.</p>', 'ridcodtoken'),
	));
	
	// Check if purchase email is enabled
	if (empty($email_settings['purchase_email_enabled'])) {
		return;
	}
	
	// Prepare placeholders
	$customer_name = $order->get_billing_first_name() . ' ' . $order->get_billing_last_name();
	$site_name = get_bloginfo('name');
	$order_number = $order->get_order_number();
	
	// Build license keys HTML
	$license_keys_html = '';
	if (!empty($licenses)) {
		$license_keys_html = '<ul style="list-style: none; padding-left: 0; margin-bottom: 20px;">';
		foreach ($licenses as $license) {
			$license_keys_html .= '<li style="margin-bottom: 10px; background-color: #f7f7f7; padding: 10px; border-radius: 4px;">';
			$license_keys_html .= '<strong>' . esc_html($license['product_name']) . ':</strong><br>';
			$license_keys_html .= '<code style="font-size: 1.1em; background-color: #eee; padding: 3px 6px; border-radius: 3px; display: inline-block; margin-top: 5px;">' . esc_html($license['key']) . '</code>';
			$license_keys_html .= '</li>';
		}
		$license_keys_html .= '</ul>';
	}	
	// Replace placeholders in subject
	$subject = str_replace(
		array('{order_number}', '{customer_name}', '{site_name}'),
		array($order_number, $customer_name, $site_name),
		$email_settings['purchase_email_subject']
	);
		// Replace placeholders in content
	$body = str_replace(
		array('{license_keys}', '{order_number}', '{customer_name}', '{site_name}'),
		array($license_keys_html, $order_number, $customer_name, $site_name),
		$email_settings['purchase_email_content']
	);

	$headers = array('Content-Type: text/html; charset=UTF-8');

	// Send the email
	wp_mail( $to_email, $subject, $body, $headers );

	// Optional: Log email sending
	// error_log("RIDCODTOKEN: Sent license email to " . $to_email . " for order ID " . $order->get_id());
}

/**
 * NOTE: This integration requires configuring the WooCommerce Product IDs
 * in the License Manager > Settings page. Licenses will be generated automatically
 * when an order containing one of these products is marked as completed.
 */
 
 /**
  * Displays license key and download link on the WooCommerce Thank You page.
  *
  * @param int $order_id The ID of the order being displayed.
  */ function ridcodtoken_display_license_on_thankyou( $order_id ) {
 	global $wpdb;
 	$order = wc_get_order( $order_id );
 
 	// Ensure it's a valid order
 	if ( ! $order ) {
 		return;
 	}
 
 	// Get download URL from settings
 	$settings = get_option( 'ridcodtoken_settings' );
 	$download_url = isset( $settings['download_url'] ) ? esc_url( $settings['download_url'] ) : '';
 
 	// Get active license plans from database
 	$table_plans = $wpdb->prefix . 'ridcod_license_plans';
 	$active_plans = $wpdb->get_results("SELECT product_id FROM $table_plans WHERE status = 'active'", ARRAY_A);
 	$license_product_ids = array_column($active_plans, 'product_id');
 
 	// If no license plans are configured, do nothing early
 	if ( empty( $license_product_ids ) ) {
 		return;
 	}
 
 	// Check if the order contains any of the license products
 	$contains_license_product = false;
 	foreach ( $order->get_items() as $item ) {
 		if ( in_array( $item->get_product_id(), $license_product_ids ) ) {
 			$contains_license_product = true;
 			break;
 		}
 	}
 
 	// If the order doesn't contain a license product, do nothing
 	if ( ! $contains_license_product ) {
 		return;
 	}
 
 	// Find the license keys associated with this order
 	$table_licenses = $wpdb->prefix . 'ridcod_licenses';
 	$licenses = $wpdb->get_results( $wpdb->prepare(
 		"SELECT license_key FROM $table_licenses WHERE order_id = %d",
 		$order_id
 	) );
 
 	// Display the information only if we found licenses or have a download URL
 	if ( ! empty( $licenses ) || ! empty( $download_url ) ) {
 		echo '<h2>' . esc_html__( 'Your Plugin License & Download', 'ridcodtoken' ) . '</h2>';
 		echo '<div class="ridcodtoken-thankyou-details" style="margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; background-color: #f9f9f9; border-radius: 4px;">';
 
 		// Display License Keys
 		if ( ! empty( $licenses ) ) {
 			echo '<h3 style="margin-top: 0;">' . esc_html__( 'License Key(s):', 'ridcodtoken' ) . '</h3>';
 			echo '<ul style="list-style: none; padding-left: 0;">';
 			foreach ( $licenses as $license ) {
 				echo '<li style="margin-bottom: 5px; background-color: #eee; padding: 5px 8px; border-radius: 3px; font-family: monospace;">' . esc_html( $license->license_key ) . '</li>';
 			}
 			echo '</ul>';
 			echo '<p><small>' . esc_html__( 'Please copy and save your license key(s). You will also receive them via email.', 'ridcodtoken' ) . '</small></p>';
 		}
 
 		// Display Download Link ONLY if order is completed
 		if ( ! empty( $download_url ) && $order->has_status( 'completed' ) ) {
 			echo '<h3 style="margin-top: 15px;">' . esc_html__( 'Download Your Plugin:', 'ridcodtoken' ) . '</h3>';
 			echo '<p><a href="' . esc_url( $download_url ) . '" class="button wc-forward" target="_blank" style="text-decoration: none; font-weight: bold;">' . esc_html__( 'Download Plugin (.zip)', 'ridcodtoken' ) . '</a></p>';
 		} elseif ( ! empty( $download_url ) ) {
 			// Message if order is not yet completed but download URL exists
 			 echo '<p><small>' . esc_html__( 'Your download link will be available here and sent via email once your order is marked as complete.', 'ridcodtoken' ) . '</small></p>';
 		}
 		// Closing div for ridcodtoken-thankyou-details
 		echo '</div>';
 	} // End if ( ! empty( $licenses ) || ! empty( $download_url ) )
 } // End function ridcodtoken_display_license_on_thankyou
 add_action( 'woocommerce_thankyou', 'ridcodtoken_display_license_on_thankyou', 10, 1 );
 
 
 // --- My Account Page Integration ---
 
 /**
  * Adds a custom endpoint ('my-licenses') to the My Account page.
  */
 function ridcodtoken_add_licenses_endpoint() {
 	add_rewrite_endpoint( 'my-licenses', EP_ROOT | EP_PAGES );
 }
 add_action( 'init', 'ridcodtoken_add_licenses_endpoint' );
 
 /**
  * Adds the 'my-licenses' endpoint to the query variables.
  *
  * @param array $vars Query variables.
  * @return array Modified query variables.
  */
 function ridcodtoken_licenses_query_vars( $vars ) {
 	$vars[] = 'my-licenses';
 	return $vars;
 }
 add_filter( 'woocommerce_get_query_vars', 'ridcodtoken_licenses_query_vars', 0 );
 
 /**
  * Adds the "My Licenses" link to the My Account menu.
  *
  * @param array $items Existing menu items.
  * @return array Modified menu items.
  */
 function ridcodtoken_add_licenses_link_my_account( $items ) {
 	// Add the new item after 'orders'
 	// Add the new item after 'downloads' or 'orders' if downloads doesn't exist
 	$anchor_key = 'downloads';
 	if (!isset($items[$anchor_key])) {
 		$anchor_key = 'orders';
 	}

 	$new_items = [];
 	$inserted = false;
 	foreach ($items as $key => $value) {
 		$new_items[$key] = $value;
 		if ($anchor_key === $key) {
 			$new_items['my-licenses'] = __( 'My Licenses', 'ridcodtoken' ); // Translate this string
 			$inserted = true;
 		}
 	}

 	// If the anchor key wasn't found, add it at the end
 	if (!$inserted) {
 			$new_items['my-licenses'] = __( 'My Licenses', 'ridcodtoken' ); // Translate this string
 	}
 	return $new_items;
 }
 add_filter( 'woocommerce_account_menu_items', 'ridcodtoken_add_licenses_link_my_account' );
 
 /**
  * Renders the content for the "My Licenses" endpoint on the My Account page.
  */
 function ridcodtoken_licenses_endpoint_content() {
 	global $wpdb;
 	$current_user = wp_get_current_user();
 
 	if ( ! $current_user || ! $current_user->user_email ) {
 		echo '<p>' . esc_html__( 'Could not retrieve your user information.', 'ridcodtoken' ) . '</p>';
 		return;
 	}
 
 	// Get download URL from settings
 	$settings = get_option( 'ridcodtoken_settings' );
 	$download_url = isset( $settings['download_url'] ) ? esc_url( $settings['download_url'] ) : '';
 
 	// Find licenses associated with the current user's email
 	$table_licenses = $wpdb->prefix . 'ridcod_licenses';
 	$table_sites = $wpdb->prefix . 'ridcod_license_sites'; // Define sites table
 
 	// Fetch licenses for the user
 	$licenses = $wpdb->get_results( $wpdb->prepare(
 		"SELECT id, license_key, sites_allowed, created_at FROM $table_licenses WHERE user_email = %s ORDER BY created_at DESC", // Select license ID as well
 		$current_user->user_email
 	) );
 
 	// Pre-fetch activation counts for all user licenses to avoid query in loop
 	$license_ids = wp_list_pluck( $licenses, 'id' );
 	$activation_counts = [];
 	if ( ! empty( $license_ids ) ) {
 		$ids_placeholder = implode( ',', array_fill( 0, count( $license_ids ), '%d' ) );
 		$counts_results = $wpdb->get_results( $wpdb->prepare(
 			"SELECT license_id, COUNT(id) as count FROM $table_sites WHERE license_id IN ($ids_placeholder) GROUP BY license_id",
 			$license_ids
 		), OBJECT_K ); // Key the results by license_id
 
 		if ( $counts_results ) {
 			foreach ( $counts_results as $license_id => $result ) {
 				$activation_counts[ $license_id ] = $result->count;
 			}
 		}
 	}
 
 	echo '<h2>' . esc_html__( 'My Licenses & Downloads', 'ridcodtoken' ) . '</h2>'; // Translate this string
 
 	// Download link display moved below, inside the license check
 
 	// Display Licenses Table
 	if ( ! empty( $licenses ) ) {

 		// Display Download Link if available (moved here)
 		if ( ! empty( $download_url ) ) {
 			echo '<div class="ridcodtoken-download-section woocommerce-message" style="margin-bottom: 25px;">'; // Use WC styles
 			echo '<h3>' . esc_html__( 'Plugin Download', 'ridcodtoken' ) . '</h3>'; // Translate
 			echo '<p>' . esc_html__( 'You can download the latest version of the plugin here:', 'ridcodtoken' ) . '</p>'; // Translate
 			echo '<p><a href="' . esc_url( $download_url ) . '" class="button woocommerce-button button wc-forward" target="_blank">' . esc_html__( 'Download Plugin (.zip)', 'ridcodtoken' ) . '</a></p>'; // Translate
 			echo '</div>';
 		} else {
 			// Optional: Message if download URL is not set but user has licenses
 			// echo '<p>' . esc_html__( 'Plugin download link is not available.', 'ridcodtoken' ) . '</p>';
 		}

 		// Display Licenses Table (original content continues)
 		echo '<h3>' . esc_html__( 'Your License Keys', 'ridcodtoken' ) . '</h3>'; // Translate
 		echo '<table class="woocommerce-table woocommerce-table--licenses shop_table shop_table_responsive my_account_licenses">'; // Add class for styling
 		echo '<thead><tr>';
 		echo '<th class="woocommerce-table__header license-key"><span class="nobr">' . esc_html__( 'License Key', 'ridcodtoken' ) . '</span></th>'; // Translate
 		echo '<th class="woocommerce-table__header sites-allowed"><span class="nobr">' . esc_html__( 'Sites Allowed', 'ridcodtoken' ) . '</span></th>'; // Translate
 		echo '<th class="woocommerce-table__header sites-used"><span class="nobr">' . esc_html__( 'Usage', 'ridcodtoken' ) . '</span></th>'; // Translate (New Column)
 		echo '<th class="woocommerce-table__header purchase-date"><span class="nobr">' . esc_html__( 'Purchase Date', 'ridcodtoken' ) . '</span></th>'; // Translate
 		echo '</tr></thead>';
 		echo '<tbody>';
 		foreach ( $licenses as $license ) {
 			$purchase_date = date_i18n( get_option( 'date_format' ), strtotime( $license->created_at ) );
 			$used_count = isset( $activation_counts[ $license->id ] ) ? $activation_counts[ $license->id ] : 0; // Get activation count
 			echo '<tr class="woocommerce-table__row license-row">';
 			echo '<td class="woocommerce-table__cell license-key" data-title="' . esc_attr__( 'License Key', 'ridcodtoken' ) . '"><code class="ridcodtoken-license-key">' . esc_html( $license->license_key ) . '</code></td>'; // Translate attr
 			echo '<td class="woocommerce-table__cell sites-allowed" data-title="' . esc_attr__( 'Sites Allowed', 'ridcodtoken' ) . '">' . esc_html( $license->sites_allowed ) . '</td>'; // Translate attr
 			echo '<td class="woocommerce-table__cell sites-used" data-title="' . esc_attr__( 'Usage', 'ridcodtoken' ) . '">' . sprintf( '%d / %d', $used_count, $license->sites_allowed ) . '</td>'; // Translate attr (New Column)
 			echo '<td class="woocommerce-table__cell purchase-date" data-title="' . esc_attr__( 'Purchase Date', 'ridcodtoken' ) . '">' . esc_html( $purchase_date ) . '</td>'; // Translate attr
 			echo '</tr>';
 		}
 		echo '</tbody>';
 		echo '</table>';
 	} else {
 		echo '<p>' . esc_html__( 'You do not have any licenses associated with this account.', 'ridcodtoken' ) . '</p>'; // Translate
 	}
 }
 // Hook the content function to the custom endpoint action
 add_action( 'woocommerce_account_my-licenses_endpoint', 'ridcodtoken_licenses_endpoint_content' );
 
 // Flush rewrite rules on plugin activation/deactivation to ensure endpoint works
 function ridcodtoken_flush_rewrite_rules() {
 	ridcodtoken_add_licenses_endpoint(); // Ensure endpoint is registered
 	flush_rewrite_rules();
 }
 register_activation_hook( RIDCODTOKEN_PLUGIN_FILE, 'ridcodtoken_flush_rewrite_rules' ); // Assuming RIDCODTOKEN_PLUGIN_FILE is defined in the main plugin file
 register_deactivation_hook( RIDCODTOKEN_PLUGIN_FILE, 'flush_rewrite_rules' );