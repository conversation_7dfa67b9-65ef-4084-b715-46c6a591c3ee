/* RIDCODTOKEN License Manager Ad<PERSON> Styles */

.ridcodtoken-license-manager {
    margin: 20px 0;
}

/* Quick Email Search Section */
.ridcodtoken-quick-email-search {
    background: linear-gradient(135deg, #f9f9f9 0%, #ffffff 100%);
    border: 1px solid #0073aa;
    border-left: 4px solid #0073aa;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 3px;
    box-shadow: 0 1px 3px rgba(0,115,170,0.1);
}

.ridcodtoken-quick-email-search h4 {
    margin-top: 0;
    margin-bottom: 8px;
    color: #0073aa;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.ridcodtoken-quick-email-search h4 small {
    font-weight: normal;
    color: #999;
    font-size: 12px;
    margin-left: 8px;
}

.ridcodtoken-quick-email-search h4 .dashicons {
    font-size: 18px;
}

.ridcodtoken-quick-email-search p {
    margin-bottom: 10px;
    color: #666;
    font-size: 14px;
}

.ridcodtoken-quick-email-search .email-search-row {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.ridcodtoken-quick-email-search input[type="email"] {
    min-width: 250px;
    border: 2px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.ridcodtoken-quick-email-search input[type="email"]:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.ridcodtoken-quick-email-search .button-primary {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
    padding: 8px 16px;
    height: auto;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 600;
}

.ridcodtoken-quick-email-search .button-primary:hover {
    background: #005177;
    border-color: #005177;
}

.ridcodtoken-quick-email-search .button-primary .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.ridcodtoken-quick-email-search .search-result-indicator {
    color: #46b450;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
    background: #e8f5e8;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #46b450;
    font-size: 14px;
    min-width: fit-content;
}

.ridcodtoken-quick-email-search .search-result-indicator .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.ridcodtoken-quick-email-search .search-result-indicator strong {
    color: #0073aa;
}

/* Statistics Cards */
.ridcodtoken-stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0 30px 0;
}

.stats-card {
    background: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stats-number {
    font-size: 2.5em;
    font-weight: 700;
    margin-bottom: 8px;
    line-height: 1;
}

.stats-label {
    font-size: 14px;
    color: #666;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-total .stats-number {
    color: #0073aa;
}

.stats-active .stats-number {
    color: #46b450;
}

.stats-inactive .stats-number {
    color: #ffb900;
}

.stats-expired .stats-number {
    color: #dc3232;
}

.ridcodtoken-license-manager .search-box {
    position: relative;
    float: right;
    margin: 0 0 10px 0;
}

.ridcodtoken-license-manager .search-box input[type="search"] {
    width: 280px;
    margin-right: 5px;
}

.ridcodtoken-license-manager .actions {
    margin-bottom: 10px;
}

.ridcodtoken-license-manager .actions select {
    margin-right: 10px;
    min-width: 150px;
}

.ridcodtoken-license-manager .tablenav-pages {
    float: right;
    display: block;
    cursor: default;
    height: 30px;
    color: #666;
    font-size: 12px;
    line-height: 30px;
}

.ridcodtoken-license-manager .tablenav-pages .displaying-num {
    margin-right: 10px;
    color: #666;
}

.ridcodtoken-license-manager .tablenav-pages .page-numbers {
    background: #f7f7f7;
    border: 1px solid #ccc;
    color: #0073aa;
    padding: 3px 8px;
    text-decoration: none;
    margin: 0 2px;
    border-radius: 3px;
}

.ridcodtoken-license-manager .tablenav-pages .page-numbers.current {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
}

.ridcodtoken-license-manager .tablenav-pages .page-numbers:hover {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
}

/* Status indicators */
.ridcodtoken-status-active {
    color: #46b450;
    font-weight: 600;
    background: #e8f5e8;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    text-transform: uppercase;
}

.ridcodtoken-status-inactive {
    color: #dc3232;
    font-weight: 600;
    background: #fbeaea;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    text-transform: uppercase;
}

/* Usage indicators */
.usage-normal {
    color: #666;
}

.usage-high {
    color: #ff8c00;
    font-weight: 600;
}

.usage-full {
    color: #dc3232;
    font-weight: 600;
    background: #fbeaea;
    padding: 2px 6px;
    border-radius: 3px;
}

/* Badge styling */
.badge {
    background: #0073aa;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
}

/* Responsive improvements */
@media screen and (max-width: 782px) {
    .ridcodtoken-stats-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .stats-card {
        padding: 15px;
    }
    
    .stats-number {
        font-size: 2em;
    }

    /* Quick Email Search Responsive */
    .ridcodtoken-quick-email-search .email-search-row {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .ridcodtoken-quick-email-search input[type="email"] {
        min-width: 100%;
        width: 100%;
    }
    
    .ridcodtoken-quick-email-search .button-primary {
        width: 100%;
        justify-content: center;
    }
    
    .ridcodtoken-quick-email-search .search-result-indicator {
        text-align: center;
        justify-content: center;
    }
    
    .ridcodtoken-license-manager .search-box {
        float: none;
        text-align: left;
        margin-bottom: 15px;
    }
    
    .ridcodtoken-license-manager .search-box input[type="search"] {
        width: 100%;
        max-width: none;
    }
    
    .ridcodtoken-license-manager .actions {
        text-align: left;
    }
    
    .ridcodtoken-license-manager .actions select {
        width: 100%;
        margin-bottom: 5px;
        margin-right: 0;
    }
}

@media screen and (max-width: 480px) {
    .ridcodtoken-stats-cards {
        grid-template-columns: 1fr;
    }

    .ridcodtoken-quick-email-search {
        padding: 12px;
    }
    
    .ridcodtoken-quick-email-search h4 {
        font-size: 14px;
    }
}

/* Table improvements */
.ridcodtoken-license-manager .widefat th {
    font-weight: 600;
    background: #f9f9f9;
}

.ridcodtoken-license-manager .widefat td {
    vertical-align: middle;
}

.ridcodtoken-license-manager .row-actions {
    color: #ddd;
}

.ridcodtoken-license-manager .row-actions span {
    display: inline;
}

.ridcodtoken-license-manager .row-actions a {
    color: #0073aa;
    text-decoration: none;
}

.ridcodtoken-license-manager .row-actions a:hover {
    color: #005177;
}

.ridcodtoken-license-manager .row-actions .delete a {
    color: #dc3232;
}

.ridcodtoken-license-manager .row-actions .delete a:hover {
    color: #a00;
}

/* Filter section styling */
.ridcodtoken-filters {
    background: #f9f9f9;
    border: 1px solid #e5e5e5;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 3px;
}

.ridcodtoken-filters .filter-row {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.ridcodtoken-filters label {
    font-weight: 600;
    margin-right: 5px;
}

.ridcodtoken-filters select {
    min-width: 150px;
}

/* Page title action button styling */
.ridcodtoken-license-manager .page-title-action {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
}

.ridcodtoken-license-manager .page-title-action:hover {
    background: #005177;
    border-color: #005177;
}

/* Empty state styling */
.ridcodtoken-license-manager .no-items {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

.ridcodtoken-quick-email-search .button-primary:disabled {
    background: #a0a5aa !important;
    border-color: #a0a5aa !important;
    cursor: not-allowed;
    opacity: 0.8;
}

.ridcodtoken-quick-email-search .button-primary .dashicons-update {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@media screen and (max-width: 782px) {
    .ridcodtoken-filters .filter-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .ridcodtoken-filters select {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .ridcodtoken-license-manager .page-title-action {
        display: block;
        text-align: center;
        margin-top: 10px;
    }
}
