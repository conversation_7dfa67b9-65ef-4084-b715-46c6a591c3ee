// JavaScript للواجهة الأمامية - إضافة اختصار الروابط
(function() {
    'use strict';
    
    // التأكد من وجود jQuery
    if (typeof jQuery === 'undefined') {
        console.warn('Ridcod Shorts: jQuery is not loaded');
        return;
    }
    
    jQuery(document).ready(function($) {
        
        // تحسين تجربة العداد التنازلي
        function enhanceCountdownExperience() {
            const overlay = $('#ridcod-countdown-overlay');
            const timer = $('#ridcod-countdown-timer');
            const continueBtn = $('#ridcod-continue-btn');
            
            if (overlay.length) {
                // إضافة تأثيرات صوتية (اختيارية)
                function playTick() {
                    // يمكن إضافة صوت تكتكة هنا إذا رغبت
                    // const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhDzqM1/LNeSs=');
                    // audio.play().catch(() => {});
                }
                
                // مراقبة تغييرات العداد
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList' || mutation.type === 'characterData') {
                            const currentTime = parseInt(timer.text());
                            if (currentTime > 0) {
                                playTick();
                                
                                // تغيير لون العداد حسب الوقت المتبقي
                                if (currentTime <= 3) {
                                    timer.css('color', '#e74c3c');
                                    timer.addClass('urgent');
                                } else if (currentTime <= 5) {
                                    timer.css('color', '#f39c12');
                                } else {
                                    timer.css('color', '#27ae60');
                                }
                            }
                        }
                    });
                });
                
                if (timer.length) {
                    observer.observe(timer[0], {
                        childList: true,
                        characterData: true,
                        subtree: true
                    });
                }
            }
        }
        
        // تحسين تجربة التمرير السلس
        function smoothScrollToTarget() {
            const redirectSection = $('#ridcod-redirect-section');
            
            if (redirectSection.length) {
                $('html, body').animate({
                    scrollTop: redirectSection.offset().top - 50
                }, 1000, 'swing');
            }
        }
        
        // تحسين إمكانية الوصول
        function enhanceAccessibility() {
            // إضافة دعم لوحة المفاتيح
            $(document).on('keydown', function(e) {
                // العداد التنازلي - مفتاح Space أو Enter للمتابعة
                if ((e.key === ' ' || e.key === 'Enter') && $('#ridcod-continue-btn').is(':visible')) {
                    e.preventDefault();
                    $('#ridcod-continue-btn').click();
                }
                
                // قسم الانتقال - مفتاح Enter للانتقال
                if (e.key === 'Enter' && $('#ridcod-redirect-btn').is(':visible')) {
                    window.location.href = $('#ridcod-redirect-btn').attr('href');
                }
                
                // إيقاف العداد بمفتاح Escape
                if (e.key === 'Escape' && $('#ridcod-countdown-overlay').is(':visible')) {
                    $('#ridcod-continue-btn').click();
                }
            });
            
            // إضافة تسميات ARIA
            $('#ridcod-countdown-timer').attr('aria-live', 'assertive');
            $('#ridcod-continue-btn').attr('aria-label', 'متابعة إلى المحتوى');
            $('#ridcod-redirect-btn').attr('aria-label', 'الانتقال إلى الرابط النهائي');
        }
        
        // منع التلاعب بالصفحة
        function preventManipulation() {
            // منع النقر بالزر الأيمن
            $(document).on('contextmenu', function(e) {
                e.preventDefault();
                return false;
            });
            
            // منع اختصارات المطور
            $(document).on('keydown', function(e) {
                // منع F12
                if (e.key === 'F12') {
                    e.preventDefault();
                    return false;
                }
                
                // منع Ctrl+Shift+I
                if (e.ctrlKey && e.shiftKey && e.key === 'I') {
                    e.preventDefault();
                    return false;
                }
                
                // منع Ctrl+U
                if (e.ctrlKey && e.key === 'u') {
                    e.preventDefault();
                    return false;
                }
                
                // منع Ctrl+S
                if (e.ctrlKey && e.key === 's') {
                    e.preventDefault();
                    return false;
                }
                
                // منع Ctrl+A
                if (e.ctrlKey && e.key === 'a') {
                    e.preventDefault();
                    return false;
                }
                
                // منع Ctrl+P
                if (e.ctrlKey && e.key === 'p') {
                    e.preventDefault();
                    return false;
                }
            });
            
            // منع تحديد النص
            $(document).on('selectstart', function(e) {
                e.preventDefault();
                return false;
            });
            
            // منع السحب والإفلات
            $(document).on('dragstart', function(e) {
                e.preventDefault();
                return false;
            });
        }
        
        // إضافة تأثيرات بصرية للأزرار
        function addButtonEffects() {
            $('.ridcod-continue-btn, .ridcod-redirect-btn').on('mouseenter', function() {
                $(this).css('transform', 'translateY(-2px) scale(1.05)');
            }).on('mouseleave', function() {
                $(this).css('transform', 'translateY(0) scale(1)');
            });
        }
        
        // تتبع التفاعل للإحصائيات
        function trackInteraction() {
            let interactionData = {
                page_loaded: new Date(),
                countdown_completed: null,
                content_viewed: null,
                redirect_clicked: null
            };
            
            // تتبع اكتمال العداد
            $('#ridcod-continue-btn').on('click', function() {
                interactionData.countdown_completed = new Date();
            });
            
            // تتبع عرض المحتوى
            $(document).on('scroll', function() {
                if ($(window).scrollTop() > 100 && !interactionData.content_viewed) {
                    interactionData.content_viewed = new Date();
                }
            });
            
            // تتبع النقر على زر الانتقال
            $('#ridcod-redirect-btn').on('click', function() {
                interactionData.redirect_clicked = new Date();
                
                // يمكن إرسال البيانات إلى الخادم هنا للإحصائيات
                // sendAnalytics(interactionData);
            });
        }
        
        // تحسين الأداء للأجهزة المحمولة
        function optimizeForMobile() {
            // كشف الأجهزة المحمولة
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            
            if (isMobile) {
                // تقليل التأثيرات المرئية للأداء الأفضل
                $('.ridcod-countdown-content').addClass('mobile-optimized');
                
                // منع التكبير
                $('meta[name=viewport]').attr('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                
                // تحسين اللمس
                $('.ridcod-continue-btn, .ridcod-redirect-btn').css({
                    'touch-action': 'manipulation',
                    'min-height': '44px',
                    'min-width': '44px'
                });
            }
        }
        
        // معالجة الأخطاء
        function handleErrors() {
            window.addEventListener('error', function(e) {
                console.error('Ridcod Shorts Error:', e.message);
            });
            
            // معالجة انقطاع الاتصال
            window.addEventListener('offline', function() {
                const notice = $('<div class="ridcod-offline-notice">تم فقدان الاتصال بالإنترنت</div>');
                $('body').prepend(notice);
                notice.fadeIn();
            });
            
            window.addEventListener('online', function() {
                $('.ridcod-offline-notice').fadeOut(function() {
                    $(this).remove();
                });
            });
        }
        
        // تهيئة جميع التحسينات
        function initialize() {
            enhanceCountdownExperience();
            enhanceAccessibility();
            preventManipulation();
            addButtonEffects();
            trackInteraction();
            optimizeForMobile();
            handleErrors();
            
            // إضافة فئة للجسم للتأكد من تحميل JavaScript
            $('body').addClass('ridcod-js-loaded');
            
            console.log('Ridcod Shorts: Frontend script loaded successfully');
        }
        
        // بدء التهيئة
        initialize();
        
        // تصدير الوظائف للاستخدام الخارجي إذا لزم الأمر
        window.RidcodShorts = {
            smoothScrollToTarget: smoothScrollToTarget,
            version: '1.0.0'
        };
    });
    
})();
