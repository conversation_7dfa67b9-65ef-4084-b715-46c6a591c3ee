/**
 * JavaScript لمركز التراخيص الموحد - RIDCODTOKEN License Center
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // متغيرات عامة
    const messages = ridcodtoken_center.messages;
    const ajaxUrl = ridcodtoken_center.ajax_url;
    const nonce = ridcodtoken_center.nonce;
    
    // تفعيل سريع من النموذج الرئيسي
    $('#ridcodtoken-quick-form').on('submit', function(e) {
        e.preventDefault();
        
        const licenseKey = $('#license-key').val().trim();
        const targetPlugin = $('#target-plugin').val();
        const productId = $('#target-plugin option:selected').data('product');
        
        if (!licenseKey) {
            showResult('#quick-activation-result', 'error', 'Please enter a license key');
            return;
        }
        
        activateLicense(licenseKey, targetPlugin, productId, '#quick-activation-result', function() {
            // إعادة تحميل الصفحة بعد التفعيل الناجح
            setTimeout(() => location.reload(), 2000);
        });
    });
    
    // تفعيل من بطاقة الإضافة
    $('.activate-license-btn').on('click', function() {
        const plugin = $(this).data('plugin');
        const productId = $(this).data('product');
        const pluginName = $(this).data('name');
        
        // ملء النافذة المنبثقة
        $('#modal-plugin-name').val(pluginName);
        $('#modal-plugin-slug').val(plugin);
        $('#modal-product-id').val(productId);
        $('#modal-license-key').val('').focus();
        $('.modal-result').hide();
        
        // عرض النافذة المنبثقة
        $('#license-activation-modal').show();
    });
    
    // تفعيل من النافذة المنبثقة
    $('#modal-activation-form').on('submit', function(e) {
        e.preventDefault();
        
        const licenseKey = $('#modal-license-key').val().trim();
        const pluginSlug = $('#modal-plugin-slug').val();
        const productId = $('#modal-product-id').val();
        
        if (!licenseKey) {
            showResult('.modal-result', 'error', 'Please enter a license key');
            return;
        }
        
        activateLicense(licenseKey, pluginSlug, productId, '.modal-result', function() {
            // إغلاق النافذة وإعادة تحميل الصفحة
            setTimeout(() => {
                $('#license-activation-modal').hide();
                location.reload();
            }, 2000);
        });
    });
    
    // إلغاء تفعيل الترخيص
    $('.deactivate-license-btn').on('click', function() {
        if (!confirm(messages.confirm_deactivate)) {
            return;
        }
        
        const plugin = $(this).data('plugin');
        const resultContainer = $(this).closest('.plugin-card').find('.plugin-result');
        
        deactivateLicense(plugin, resultContainer);
    });
    
    // فحص ترخيص واحد
    $('.check-license-btn').on('click', function() {
        const plugin = $(this).data('plugin');
        const productId = $(this).data('product');
        const resultContainer = $(this).closest('.plugin-card').find('.plugin-result');
        
        checkSingleLicense(plugin, productId, resultContainer);
    });
    
    // فحص جميع التراخيص
    $('#check-all-licenses').on('click', function() {
        checkAllLicenses();
    });
    
    // إغلاق النافذة المنبثقة
    $('.modal-close').on('click', function() {
        $('#license-activation-modal').hide();
    });
    
    // إغلاق النافذة عند النقر خارجها
    $(window).on('click', function(e) {
        if ($(e.target).is('#license-activation-modal')) {
            $('#license-activation-modal').hide();
        }
    });
    
    // إغلاق النافذة بمفتاح Escape
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            $('#license-activation-modal').hide();
        }
    });
    
    // تحديث قائمة الإضافات عند تغيير الترخيص
    $('#target-plugin').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const productId = selectedOption.data('product');
        
        if (productId) {
            console.log('Selected plugin product ID:', productId);
        }
    });
    
    /**
     * تفعيل ترخيص
     */
    function activateLicense(licenseKey, pluginSlug, productId, resultContainer, successCallback) {
        showResult(resultContainer, 'loading', messages.activating);
        
        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'ridcodtoken_unified_activate',
                nonce: nonce,
                license_key: licenseKey,
                plugin_slug: pluginSlug,
                product_id: productId
            },
            success: function(response) {
                if (response.success) {
                    showResult(resultContainer, 'success', response.data.message || messages.success);
                    if (successCallback) {
                        successCallback(response.data);
                    }
                } else {
                    const errorMessage = response.data && response.data.message ? response.data.message : messages.error;
                    showResult(resultContainer, 'error', errorMessage);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                showResult(resultContainer, 'error', 'Connection error occurred. Please try again.');
            }
        });
    }
    
    /**
     * إلغاء تفعيل ترخيص
     */
    function deactivateLicense(pluginSlug, resultContainer) {
        showResult(resultContainer, 'loading', messages.deactivating);
        
        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'ridcodtoken_unified_deactivate',
                nonce: nonce,
                plugin_slug: pluginSlug
            },
            success: function(response) {
                if (response.success) {
                    showResult(resultContainer, 'success', response.data.message || messages.success);
                    // إعادة تحميل الصفحة بعد 2 ثانية
                    setTimeout(() => location.reload(), 2000);
                } else {
                    const errorMessage = response.data && response.data.message ? response.data.message : messages.error;
                    showResult(resultContainer, 'error', errorMessage);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                showResult(resultContainer, 'error', 'Connection error occurred. Please try again.');
            }
        });
    }
    
    /**
     * فحص ترخيص واحد
     */
    function checkSingleLicense(pluginSlug, productId, resultContainer) {
        showResult(resultContainer, 'loading', messages.checking);
        
        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'ridcodtoken_unified_check_single',
                nonce: nonce,
                plugin_slug: pluginSlug,
                product_id: productId
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    if (data.is_valid) {
                        showResult(resultContainer, 'success', 'License is valid and active');
                    } else {
                        showResult(resultContainer, 'error', data.message || 'License is not valid');
                    }
                } else {
                    const errorMessage = response.data && response.data.message ? response.data.message : 'Failed to check license';
                    showResult(resultContainer, 'error', errorMessage);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                showResult(resultContainer, 'error', 'Connection error occurred. Please try again.');
            }
        });
    }
    
    /**
     * فحص جميع التراخيص
     */
    function checkAllLicenses() {
        const $button = $('#check-all-licenses');
        const originalText = $button.html();
        
        // تعطيل الزر وتغيير النص
        $button.prop('disabled', true).html('<span class="dashicons dashicons-update"></span> Checking...');
        
        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'ridcodtoken_unified_check_all',
                nonce: nonce
            },
            success: function(response) {
                $button.prop('disabled', false).html(originalText);
                
                if (response.success) {
                    // عرض رسالة نجاح
                    showGlobalMessage('success', 'All licenses checked successfully');
                    
                    // إعادة تحميل الصفحة لعرض النتائج المحدثة
                    setTimeout(() => location.reload(), 1500);
                } else {
                    const errorMessage = response.data && response.data.message ? response.data.message : 'Failed to check licenses';
                    showGlobalMessage('error', errorMessage);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                $button.prop('disabled', false).html(originalText);
                showGlobalMessage('error', 'Connection error occurred. Please try again.');
            }
        });
    }
    
    /**
     * عرض نتيجة العملية
     */
    function showResult(container, type, message) {
        const $container = $(container);
        
        // إزالة الفئات السابقة وإضافة الجديدة
        $container.removeClass('success error loading').addClass(type);
        
        // عرض الرسالة
        $container.html('<p>' + message + '</p>').show();
        
        // إخفاء الرسالة تلقائياً بعد 5 ثوان (إلا إذا كانت loading)
        if (type === 'success' || type === 'error') {
            setTimeout(() => $container.fadeOut(), 5000);
        }
    }
    
    /**
     * عرض رسالة عامة في أعلى الصفحة
     */
    function showGlobalMessage(type, message) {
        const noticeClass = type === 'success' ? 'notice-success' : 'notice-error';
        const $notice = $('<div class="notice ' + noticeClass + ' is-dismissible"><p>' + message + '</p></div>');
        
        // إضافة الرسالة في أعلى الصفحة
        $('.wrap').prepend($notice);
        
        // إضافة وظيفة الإغلاق
        $notice.find('.notice-dismiss').on('click', function() {
            $notice.fadeOut();
        });
        
        // إخفاء تلقائي بعد 5 ثوان
        setTimeout(() => $notice.fadeOut(), 5000);
    }
    
    /**
     * تحديث عداد الإحصائيات
     */
    function updateStats() {
        // يمكن إضافة منطق لتحديث الإحصائيات بدون إعادة تحميل الصفحة
        // مؤقتاً سنعيد تحميل الصفحة
        location.reload();
    }
    
    /**
     * تنسيق مفتاح الترخيص للعرض
     */
    function formatLicenseKey(key) {
        if (!key || key.length < 16) {
            return key;
        }
        return key.substring(0, 8) + '****' + key.substring(key.length - 8);
    }
    
    /**
     * التحقق من صحة مفتاح الترخيص
     */
    function isValidLicenseKey(key) {
        // تحقق بسيط من طول المفتاح
        return key && key.length >= 16 && /^[a-zA-Z0-9\-]+$/.test(key);
    }
    
    // إضافة تحقق من صحة المفتاح أثناء الكتابة
    $('#license-key, #modal-license-key').on('input', function() {
        const $input = $(this);
        const key = $input.val().trim();
        
        if (key.length > 0 && !isValidLicenseKey(key)) {
            $input.addClass('invalid');
        } else {
            $input.removeClass('invalid');
        }
    });
    
    // تحسين تجربة المستخدم - تركيز تلقائي
    $('#license-activation-modal').on('shown', function() {
        $('#modal-license-key').focus();
    });
    
    // منع إرسال النموذج بمفاتيح غير صحيحة
    $('form').on('submit', function(e) {
        const $form = $(this);
        const $licenseInput = $form.find('input[type="text"]').first();
        const key = $licenseInput.val().trim();
        
        if (key && !isValidLicenseKey(key)) {
            e.preventDefault();
            showResult($form.find('.activation-result, .modal-result'), 'error', 'Please enter a valid license key');
            $licenseInput.focus();
        }
    });
    
    console.log('RIDCODTOKEN License Center initialized successfully');
});
