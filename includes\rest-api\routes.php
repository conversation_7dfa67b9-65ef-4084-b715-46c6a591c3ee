<?php
/**
 * REST API Route Registration and Callbacks
 *
 * @package RIDCODTOKEN
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Register REST API routes.
 */
function ridcodtoken_register_rest_routes() {
	$namespace = 'ridcodtoken/v1';

	// Activate License Endpoint
	register_rest_route( $namespace, '/activate', array(
		'methods'             => WP_REST_Server::CREATABLE, // POST
		'callback'            => 'ridcodtoken_rest_activate_license',
		'permission_callback' => '__return_true', // Allow public access for now, consider adding checks later
		'args'                => array(
			'license_key' => array(
				'required'          => true,
				'validate_callback' => function($param, $request, $key) {
					return is_string( $param ) && ! empty( trim( $param ) );
				},
				'sanitize_callback' => 'sanitize_text_field',
			),
			'site_url' => array(
				'required'          => true,
				'validate_callback' => function($param, $request, $key) {
					// Basic URL validation
					return filter_var( $param, FILTER_VALIDATE_URL ) !== false;
				},
				'sanitize_callback' => 'esc_url_raw',
			),
		),
	) );

	// Deactivate License Endpoint
	register_rest_route( $namespace, '/deactivate', array(
		'methods'             => WP_REST_Server::CREATABLE, // POST
		'callback'            => 'ridcodtoken_rest_deactivate_license',
		'permission_callback' => '__return_true', // Allow public access
		'args'                => array(
			'license_key' => array(
				'required'          => true,
				'validate_callback' => function($param, $request, $key) {
					return is_string( $param ) && ! empty( trim( $param ) );
				},
				'sanitize_callback' => 'sanitize_text_field',
			),
			'site_url' => array(
				'required'          => true,
				'validate_callback' => function($param, $request, $key) {
					return filter_var( $param, FILTER_VALIDATE_URL ) !== false;
				},
				'sanitize_callback' => 'esc_url_raw',
			),
		),
	) );

	// Check License Status Endpoint
	register_rest_route( $namespace, '/status', array(
		'methods'             => WP_REST_Server::READABLE, // GET
		'callback'            => 'ridcodtoken_rest_check_license_status',
		'permission_callback' => '__return_true', // Allow public access
		'args'                => array(
			'license_key' => array(
				'required'          => true,
				'validate_callback' => function($param, $request, $key) {
					return is_string( $param ) && ! empty( trim( $param ) );
				},
				'sanitize_callback' => 'sanitize_text_field',
			),
			'site_url' => array( // Optional for status check, but might be useful
				'required'          => false,
				'validate_callback' => function($param, $request, $key) {
					// Allow empty or valid URL
					return empty($param) || filter_var( $param, FILTER_VALIDATE_URL ) !== false;
				},
				'sanitize_callback' => 'esc_url_raw',
			),
		),
	) );
}

// --- REST API Callback Functions ---

/**
 * Handles license activation requests.
 *
 * @param WP_REST_Request $request Full data about the request.
 * @return WP_REST_Response|WP_Error Response object on success, or WP_Error object on failure.
 */
function ridcodtoken_rest_activate_license( WP_REST_Request $request ) {
	global $wpdb;
	$license_key = $request['license_key'];
	$site_url    = $request['site_url'];
	$product_id  = $request['product_id'];
	$table_licenses = $wpdb->prefix . 'ridcod_licenses';
	$table_sites    = $wpdb->prefix . 'ridcod_license_sites';
	$table_license_products = $wpdb->prefix . 'ridcod_license_products';

	// 1. Find license by license_key
	$license = $wpdb->get_row( $wpdb->prepare(
		"SELECT id, status, sites_allowed, expires_at, product_id FROM $table_licenses WHERE license_key = %s",
		$license_key
	) );

	// 1.1. Check if product_id is provided and validate it
	if ( empty( $product_id ) || ! is_numeric( $product_id ) ) {
		return new WP_REST_Response( array(
			'activated' => false,
			'message'   => __( 'Product ID is required and must be valid.', 'ridcodtoken' ),
		), 400 ); // Bad Request
	}

	// 2. Check if license exists
	if ( ! $license ) {
		// Return expected JSON format for failure
		return new WP_REST_Response( array(
			'activated' => false,
			'message'   => __( 'Invalid license key.', 'ridcodtoken' ),
		), 404 ); // Not Found
	}

	// 2.1. Check if license is linked to the requested product
	if ( $license->product_id && $license->product_id != $product_id ) {
		// License exists but for a different product
		return new WP_REST_Response( array(
			'activated' => false,
			'message'   => __( 'This license key is not valid for this product.', 'ridcodtoken' ),
		), 403 ); // Forbidden
	}

	// 2.2. If license doesn't have a product_id set, check if it's linked via license_products table
	if ( ! $license->product_id ) {
		$linked_product = $wpdb->get_var( $wpdb->prepare(
			"SELECT product_id FROM $table_license_products WHERE license_id = %d AND product_id = %d",
			$license->id, $product_id
		) );

		if ( ! $linked_product ) {
			// License is not linked to this product
			return new WP_REST_Response( array(
				'activated' => false,
				'message'   => __( 'This license key is not valid for this product.', 'ridcodtoken' ),
			), 403 ); // Forbidden
		}
	}

	// Check status
	if ( 'active' !== $license->status ) {
		// Return expected JSON format for failure
		return new WP_REST_Response( array(
			'activated' => false,
			'message'   => __( 'License is not active.', 'ridcodtoken' ),
		), 403 ); // Forbidden
	}

	// Check expiration
	if ( $license->expires_at && strtotime( $license->expires_at ) < time() ) {
		// Optionally update status to 'expired' here
		// $wpdb->update( $table_licenses, array( 'status' => 'expired' ), array( 'id' => $license->id ) );
		// Return expected JSON format for failure
		return new WP_REST_Response( array(
			'activated' => false,
			'message'   => __( 'License has expired.', 'ridcodtoken' ),
		), 403 ); // Forbidden
	}
	// 3. Get current activations
	$current_activations = (int) $wpdb->get_var( $wpdb->prepare(
		"SELECT COUNT(id) FROM $table_sites WHERE license_id = %d",
		$license->id
	) );

	// 4. Check if site_url is already activated
	$site_already_activated = (int) $wpdb->get_var( $wpdb->prepare(
		"SELECT COUNT(id) FROM $table_sites WHERE license_id = %d AND site_url = %s",
		$license->id,
		$site_url
	) );

	if ( $site_already_activated > 0 ) {
		// Site already active, return success format
		return new WP_REST_Response( array(
			'activated' => true,
			'message'   => __( 'Site is already activated for this license.', 'ridcodtoken' ),
		), 200 );
	}

	// 5. Check if activation limit is reached
	if ( $current_activations >= $license->sites_allowed ) {
		// Return expected JSON format for failure
		return new WP_REST_Response( array(
			'activated' => false,
			'message'   => __( 'Activation limit reached for this license.', 'ridcodtoken' ),
		), 403 ); // Forbidden
	}

	// 6. Insert site activation record
	$inserted = $wpdb->insert(
		$table_sites,
		array(
			'license_id' => $license->id,
			'site_url'   => $site_url,
			'product_id' => $product_id,
			'activated_at' => current_time( 'mysql', 1 ), // GMT time
		),
		array(
			'%d', // license_id
			'%s', // site_url
			'%d', // product_id
			'%s', // activated_at
		)
	);

	if ( false === $inserted ) {
		// Return expected JSON format for failure
		return new WP_REST_Response( array(
			'activated' => false,
			'message'   => __( 'Could not record site activation (Database Error).', 'ridcodtoken' ),
		), 500 ); // Internal Server Error
	}

	// 7. Return success in the expected format
	return new WP_REST_Response( array(
		'activated' => true,
		'message'   => __( 'License activated successfully.', 'ridcodtoken' ),
		// Optionally include expiry date if needed by client
		// 'expires_at' => $license->expires_at
	), 200 );
}

/**
 * Handles license deactivation requests.
 *
 * @param WP_REST_Request $request Full data about the request.
 * @return WP_REST_Response|WP_Error Response object on success, or WP_Error object on failure.
 */
function ridcodtoken_rest_deactivate_license( WP_REST_Request $request ) {
	global $wpdb;
	$license_key = $request['license_key'];
	$site_url    = $request['site_url'];
	$table_licenses = $wpdb->prefix . 'ridcod_licenses';
	$table_sites    = $wpdb->prefix . 'ridcod_license_sites';

	// 1 & 2. Find license ID by license_key
	$license_id = $wpdb->get_var( $wpdb->prepare(
		"SELECT id FROM $table_licenses WHERE license_key = %s",
		$license_key
	) );

	if ( ! $license_id ) {
		// License doesn't exist, arguably the site is already "deactivated" for it.
		// Return success to make client-side logic simpler.
		return new WP_REST_Response( array(
			'success' => true,
			'code'    => 'license_not_found',
			'message' => __( 'License key not found, site considered deactivated.', 'ridcodtoken' ),
		), 200 );
		// Alternatively, return an error:
		// return new WP_Error( 'invalid_license', __( 'Invalid license key.', 'ridcodtoken' ), array( 'status' => 404 ) );
	}

	// 3. Delete the site activation record
	$deleted = $wpdb->delete(
		$table_sites,
		array(
			'license_id' => $license_id,
			'site_url'   => $site_url,
		),
		array(
			'%d', // license_id
			'%s', // site_url
		)
	);

	// 4 & 5. Check result and return response
	if ( false === $deleted ) {
		return new WP_Error( 'db_error', __( 'Could not remove site activation.', 'ridcodtoken' ), array( 'status' => 500 ) );
	} elseif ( $deleted > 0 ) {
		// Deletion successful
		return new WP_REST_Response( array(
			'success' => true,
			'code'    => 'deactivated',
			'message' => __( 'License deactivated successfully for this site.', 'ridcodtoken' ),
		), 200 );
	} else {
		// No rows deleted - site wasn't active for this license anyway
		return new WP_REST_Response( array(
			'success' => true,
			'code'    => 'not_active',
			'message' => __( 'Site was not active for this license.', 'ridcodtoken' ),
		), 200 );
	}
}

/**
 * Handles license status check requests.
 *
 * @param WP_REST_Request $request Full data about the request.
 * @return WP_REST_Response|WP_Error Response object on success, or WP_Error object on failure.
 */
function ridcodtoken_rest_check_license_status( WP_REST_Request $request ) {
	global $wpdb;
	$license_key = $request['license_key'];
	$site_url = $request['site_url']; // Get site_url from request
	$product_id = $request['product_id']; // Get product_id from request
	$table_licenses = $wpdb->prefix . 'ridcod_licenses';
	$table_sites    = $wpdb->prefix . 'ridcod_license_sites';
	$table_license_products = $wpdb->prefix . 'ridcod_license_products';

	// 1. Find license by license_key
	$license = $wpdb->get_row( $wpdb->prepare(
		"SELECT id, status, sites_allowed, expires_at, product_id FROM $table_licenses WHERE license_key = %s",
		$license_key
	) );

	// 1.1. Check if product_id is provided and validate it
	if ( empty( $product_id ) || ! is_numeric( $product_id ) ) {
		return new WP_REST_Response( array(
			'status' => 'invalid',
			'site_is_active' => false,
			'message' => __( 'Product ID is required and must be valid.', 'ridcodtoken' ),
		), 400 );
	}

	// 2. If not found, return error
	if ( ! $license ) {
		// Return a specific format indicating license not found
		return new WP_REST_Response( array(
			'status'         => 'not_found',
			'site_is_active' => false,
			'is_expired'     => false, // Or true/null depending on desired logic
			'message'        => __( 'Invalid license key.', 'ridcodtoken' ),
		), 404 );
	}

	// 2.1. Check if license is linked to the requested product
	if ( $license->product_id && $license->product_id != $product_id ) {
		// License exists but for a different product
		return new WP_REST_Response( array(
			'status'         => 'invalid_product',
			'site_is_active' => false,
			'is_expired'     => false,
			'message'        => __( 'This license key is not valid for this product.', 'ridcodtoken' ),
		), 403 );
	}

	// 2.2. If license doesn't have a product_id set, check if it's linked via license_products table
	if ( ! $license->product_id ) {
		$linked_product = $wpdb->get_var( $wpdb->prepare(
			"SELECT product_id FROM $table_license_products WHERE license_id = %d AND product_id = %d",
			$license->id, $product_id
		) );

		if ( ! $linked_product ) {
			// License is not linked to this product
			return new WP_REST_Response( array(
				'status'         => 'invalid_product',
				'site_is_active' => false,
				'is_expired'     => false,
				'message'        => __( 'This license key is not valid for this product.', 'ridcodtoken' ),
			), 403 );
		}
	}

	// 3. Get license details (already done in $license)

	// 4. Count current activations
	$current_activations = (int) $wpdb->get_var( $wpdb->prepare(
		"SELECT COUNT(id) FROM $table_sites WHERE license_id = %d",
		$license->id
	) );

	// 5. Calculate remaining activations
	$sites_remaining = $license->sites_allowed - $current_activations;
	// Ensure it doesn't go below zero if something went wrong
	$sites_remaining = max( 0, $sites_remaining );

	// 6. Check if the specific site_url is activated for this license
	$site_is_active = false;
	if ($site_url && $license->id) {
		$site_activation_count = (int) $wpdb->get_var( $wpdb->prepare(
			"SELECT COUNT(id) FROM $table_sites WHERE license_id = %d AND site_url = %s AND (product_id = %d OR product_id IS NULL)",
			$license->id,
			$site_url,
			$product_id
		) );
		if ($site_activation_count > 0) {
			$site_is_active = true;
		}
	}


	// 7. Check if expired
	$is_expired = false;
	if ( $license->expires_at && strtotime( $license->expires_at ) < time() ) {
		$is_expired = true;
		// Note: We report the stored status, but also indicate if it's effectively expired now.
		// The cron job in RIDCODE should handle this combined logic.
	}

	// 8. Return status details in the format expected by the cron job
	$response_data = array(
		// 'success'         => true, // Not strictly needed by the cron job
		'status'          => $license->status, // The status stored in the DB ('active', 'inactive', 'expired', etc.)
		'site_is_active'  => $site_is_active,  // Is the requesting site currently activated?
		'is_expired'      => $is_expired,      // Has the license expiry date passed?
		'expires_at'      => $license->expires_at, // Optional: Send expiry date
		'message'         => __( 'License status retrieved.', 'ridcodtoken' ), // Optional: For debugging
	);
	return new WP_REST_Response( $response_data, 200 );
}