<?php
/**
 * RIDCODTOKEN Unified License Center
 * مركز التراخيص الموحد لجميع إضافات RIDCODTOKEN
 * 
 * @package RIDCODTOKEN
 * @version 1.0.0
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

/**
 * فئة مركز التراخيص الموحد
 */
class RIDCODTOKEN_Unified_License_Center {
    
    /**
     * الإضافات المكتشفة
     */
    private $detected_plugins = array();
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_unified_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_ridcodtoken_unified_activate', array($this, 'ajax_activate_license'));
        add_action('wp_ajax_ridcodtoken_unified_deactivate', array($this, 'ajax_deactivate_license'));
        add_action('wp_ajax_ridcodtoken_unified_check_all', array($this, 'ajax_check_all_licenses'));
        add_action('admin_notices', array($this, 'unified_admin_notices'));
        
        // كشف الإضافات عند التحميل
        $this->detect_ridcodtoken_plugins();
    }
    
    /**
     * إضافة القائمة الموحدة
     */
    public function add_unified_menu() {
        add_menu_page(
            'RIDCODTOKEN License Center',
            'License Center',
            'manage_options',
            'ridcodtoken-center',
            array($this, 'render_center_page'),
            'dashicons-admin-network',
            25
        );
        
        // إضافة صفحات فرعية
        add_submenu_page(
            'ridcodtoken-center',
            'All Licenses',
            'All Licenses',
            'manage_options',
            'ridcodtoken-center',
            array($this, 'render_center_page')
        );
        
        add_submenu_page(
            'ridcodtoken-center',
            'Statistics',
            'Statistics',
            'manage_options',
            'ridcodtoken-center-stats',
            array($this, 'render_stats_page')
        );
    }
    
    /**
     * تحميل السكريبتات والأنماط
     */
    public function enqueue_scripts($hook) {
        if (strpos($hook, 'ridcodtoken-center') === false) {
            return;
        }
        
        wp_enqueue_script('ridcodtoken-center', RIDCODTOKEN_PLUGIN_URL . 'assets/js/license-center.js', array('jquery'), RIDCODTOKEN_VERSION, true);
        wp_enqueue_style('ridcodtoken-center', RIDCODTOKEN_PLUGIN_URL . 'assets/css/license-center.css', array(), RIDCODTOKEN_VERSION);
        
        wp_localize_script('ridcodtoken-center', 'ridcodtoken_center', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ridcodtoken_center_nonce'),
            'messages' => array(
                'activating' => __('Activating license...', 'ridcodtoken'),
                'deactivating' => __('Deactivating license...', 'ridcodtoken'),
                'checking' => __('Checking licenses...', 'ridcodtoken'),
                'success' => __('Success!', 'ridcodtoken'),
                'error' => __('Error occurred!', 'ridcodtoken'),
                'confirm_deactivate' => __('Are you sure you want to deactivate this license?', 'ridcodtoken')
            )
        ));
    }
    
    /**
     * كشف جميع إضافات RIDCODTOKEN المثبتة
     */
    private function detect_ridcodtoken_plugins() {
        $this->detected_plugins = array();
        
        if (!function_exists('get_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }
        
        $all_plugins = get_plugins();
        
        foreach ($all_plugins as $plugin_file => $plugin_data) {
            $plugin_dir = dirname(WP_PLUGIN_DIR . '/' . $plugin_file);
            $plugin_slug = dirname($plugin_file);
            
            // البحث عن ملف SDK
            $sdk_paths = array(
                $plugin_dir . '/sdk/ridcodtoken-plugin-sdk.php',
                $plugin_dir . '/ridcodtoken-plugin-sdk.php',
                $plugin_dir . '/includes/ridcodtoken-plugin-sdk.php'
            );
            
            $has_sdk = false;
            foreach ($sdk_paths as $sdk_path) {
                if (file_exists($sdk_path)) {
                    $has_sdk = true;
                    break;
                }
            }
            
            if ($has_sdk) {
                // محاولة الحصول على معلومات الترخيص
                $license_info = $this->get_plugin_license_info($plugin_slug);
                
                $this->detected_plugins[] = array(
                    'file' => $plugin_file,
                    'slug' => $plugin_slug,
                    'name' => $plugin_data['Name'],
                    'version' => $plugin_data['Version'],
                    'description' => $plugin_data['Description'],
                    'is_active' => is_plugin_active($plugin_file),
                    'license_key' => $license_info['license_key'],
                    'license_status' => $license_info['license_status'],
                    'product_id' => $license_info['product_id'],
                    'last_check' => $license_info['last_check']
                );
            }
        }
        
        return $this->detected_plugins;
    }
    
    /**
     * الحصول على معلومات ترخيص إضافة معينة
     */
    private function get_plugin_license_info($plugin_slug) {
        $prefix = 'ridcodtoken_' . sanitize_key($plugin_slug) . '_';
        
        return array(
            'license_key' => get_option($prefix . 'license_key', ''),
            'license_status' => get_option($prefix . 'license_status', 'inactive'),
            'last_check' => get_option($prefix . 'last_check', 0),
            'product_id' => $this->guess_product_id($plugin_slug)
        );
    }
    
    /**
     * تخمين معرف المنتج من اسم الإضافة
     */
    private function guess_product_id($plugin_slug) {
        // قاعدة بيانات معرفات المنتجات المعروفة
        $known_products = array(
            'ridcod-shorts' => 222,
            'ridcod-seo' => 333,
            'ridcod-backup' => 444,
            // يمكن إضافة المزيد هنا
        );
        
        return isset($known_products[$plugin_slug]) ? $known_products[$plugin_slug] : 0;
    }
    
    /**
     * عرض الصفحة الرئيسية للمركز
     */
    public function render_center_page() {
        $plugins = $this->detected_plugins;
        $active_count = count(array_filter($plugins, function($p) { return $p['license_status'] === 'active'; }));
        $inactive_count = count($plugins) - $active_count;
        
        include RIDCODTOKEN_PLUGIN_DIR . 'includes/license-center/center-page.php';
    }
    
    /**
     * عرض صفحة الإحصائيات
     */
    public function render_stats_page() {
        $plugins = $this->detected_plugins;
        include RIDCODTOKEN_PLUGIN_DIR . 'includes/license-center/stats-page.php';
    }
    
    /**
     * معالج AJAX لتفعيل الترخيص
     */
    public function ajax_activate_license() {
        check_ajax_referer('ridcodtoken_center_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'ليس لديك صلاحية لهذا الإجراء'));
        }
        
        $license_key = sanitize_text_field($_POST['license_key']);
        $plugin_slug = sanitize_text_field($_POST['plugin_slug']);
        $product_id = intval($_POST['product_id']);
        
        if (empty($license_key)) {
            wp_send_json_error(array('message' => 'مفتاح الترخيص مطلوب'));
        }
        
        // إذا لم يتم تحديد الإضافة، حاول الكشف التلقائي
        if (empty($plugin_slug) || empty($product_id)) {
            $auto_detect = $this->auto_detect_plugin_from_license($license_key);
            if ($auto_detect['success']) {
                $plugin_slug = $auto_detect['plugin_slug'];
                $product_id = $auto_detect['product_id'];
            } else {
                wp_send_json_error($auto_detect);
            }
        }
        
        $result = $this->activate_license_for_plugin($license_key, $plugin_slug, $product_id);
        
        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }
    
    /**
     * معالج AJAX لإلغاء تفعيل الترخيص
     */
    public function ajax_deactivate_license() {
        check_ajax_referer('ridcodtoken_center_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'ليس لديك صلاحية لهذا الإجراء'));
        }
        
        $plugin_slug = sanitize_text_field($_POST['plugin_slug']);
        
        $result = $this->deactivate_license_for_plugin($plugin_slug);
        
        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }
    
    /**
     * معالج AJAX لفحص جميع التراخيص
     */
    public function ajax_check_all_licenses() {
        check_ajax_referer('ridcodtoken_center_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'ليس لديك صلاحية لهذا الإجراء'));
        }
        
        $results = $this->bulk_license_check();
        wp_send_json_success($results);
    }
    
    /**
     * تفعيل ترخيص لإضافة محددة
     */
    private function activate_license_for_plugin($license_key, $plugin_slug, $product_id) {
        // التحقق من صحة الترخيص مع الخادم
        $api_response = $this->verify_license_with_server($license_key, $product_id);
        
        if ($api_response['success']) {
            // حفظ الترخيص للإضافة المحددة
            $prefix = 'ridcodtoken_' . sanitize_key($plugin_slug) . '_';
            update_option($prefix . 'license_key', $license_key);
            update_option($prefix . 'license_status', 'active');
            update_option($prefix . 'last_check', time());
            
            return array('success' => true, 'message' => 'تم تفعيل الترخيص بنجاح');
        }
        
        return $api_response;
    }
    
    /**
     * إلغاء تفعيل ترخيص لإضافة محددة
     */
    private function deactivate_license_for_plugin($plugin_slug) {
        $prefix = 'ridcodtoken_' . sanitize_key($plugin_slug) . '_';
        
        // حذف بيانات الترخيص
        delete_option($prefix . 'license_key');
        update_option($prefix . 'license_status', 'inactive');
        
        return array('success' => true, 'message' => 'تم إلغاء تفعيل الترخيص بنجاح');
    }
    
    /**
     * التحقق من الترخيص مع الخادم
     */
    private function verify_license_with_server($license_key, $product_id) {
        $api_url = 'https://ridcod.com/wp-json/ridcodtoken/v1/activate'; // يجب تغيير هذا
        
        $response = wp_remote_post($api_url, array(
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/json',
                'User-Agent' => 'RIDCODTOKEN-CENTER/' . RIDCODTOKEN_VERSION
            ),
            'body' => json_encode(array(
                'license_key' => $license_key,
                'site_url' => home_url(),
                'product_id' => $product_id
            ))
        ));
        
        if (is_wp_error($response)) {
            return array('success' => false, 'message' => 'فشل في الاتصال بالخادم: ' . $response->get_error_message());
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (!$data) {
            return array('success' => false, 'message' => 'استجابة غير صحيحة من الخادم');
        }
        
        if (isset($data['activated']) && $data['activated'] === true) {
            return array('success' => true, 'message' => 'تم التحقق من الترخيص بنجاح');
        } else {
            $message = isset($data['message']) ? $data['message'] : 'فشل في التحقق من الترخيص';
            return array('success' => false, 'message' => $message);
        }
    }
    
    /**
     * الكشف التلقائي عن الإضافة من الترخيص
     */
    private function auto_detect_plugin_from_license($license_key) {
        // هنا يمكن إضافة منطق للكشف عن المنتج من الترخيص
        // مؤقتاً سنرجع خطأ
        return array('success' => false, 'message' => 'يرجى تحديد الإضافة يدوياً');
    }
    
    /**
     * فحص جميع التراخيص دفعة واحدة
     */
    private function bulk_license_check() {
        $results = array();
        
        foreach ($this->detected_plugins as $plugin) {
            if ($plugin['license_status'] === 'active' && !empty($plugin['license_key'])) {
                $check_result = $this->verify_license_with_server($plugin['license_key'], $plugin['product_id']);
                $results[$plugin['slug']] = $check_result;
                
                // تحديث الحالة بناءً على النتيجة
                if (!$check_result['success']) {
                    $prefix = 'ridcodtoken_' . sanitize_key($plugin['slug']) . '_';
                    update_option($prefix . 'license_status', 'inactive');
                }
            }
        }
        
        return $results;
    }
    
    /**
     * عرض الإشعارات الموحدة
     */
    public function unified_admin_notices() {
        // عدم عرض الإشعارات في صفحة المركز نفسها
        $screen = get_current_screen();
        if ($screen && strpos($screen->id, 'ridcodtoken-center') !== false) {
            return;
        }
        
        $inactive_plugins = array_filter($this->detected_plugins, function($p) {
            return $p['is_active'] && $p['license_status'] !== 'active';
        });
        
        if (!empty($inactive_plugins)) {
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p><strong>RIDCODTOKEN License Center:</strong> ';
            echo sprintf('لديك %d إضافة تحتاج لتفعيل الترخيص. ', count($inactive_plugins));
            echo '<a href="' . admin_url('admin.php?page=ridcodtoken-center') . '" class="button button-small">إدارة التراخيص</a>';
            echo '</p>';
            echo '</div>';
        }
    }
    
    /**
     * الحصول على جميع الإضافات المكتشفة
     */
    public function get_detected_plugins() {
        return $this->detected_plugins;
    }
}

// تهيئة المركز الموحد
new RIDCODTOKEN_Unified_License_Center();
