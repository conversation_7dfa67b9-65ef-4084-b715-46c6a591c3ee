<?php
/**
 * Plugin Name:       R<PERSON>CODTOKEN
 * Plugin URI:        https://example.com/plugins/the-basics/
 * Description:       Handle the basics with this plugin.
 * Version:           1.1.0
 * Requires at least: 5.2
 * Requires PHP:      7.2
 * Author:            Your Name
 * Author URI:        https://author.example.com/
 * License:           GPL v2 or later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Update URI:        https://example.com/my-plugin/
 * Text Domain:       ridcodtoken
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

// Define plugin constants
define( 'RIDCODTOKEN_VERSION', '1.1.0' ); // Updated version
define( 'RIDCODTOKEN_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'RIDCODTOKEN_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'RIDCODTOKEN_PLUGIN_FILE', __FILE__ ); // Define main plugin file path

// Include necessary files
// Core plugin files
require_once RIDCODTOKEN_PLUGIN_DIR . 'includes/utils.php'; // Utility functions (like key generation)
require_once RIDCODTOKEN_PLUGIN_DIR . 'includes/database/activation.php';
require_once RIDCODTOKEN_PLUGIN_DIR . 'includes/rest-api/routes.php';
require_once RIDCODTOKEN_PLUGIN_DIR . 'includes/cron/scheduled-tasks.php'; // Include Cron tasks
// WooCommerce Integration (Load only if WooCommerce is active)
function ridcodtoken_load_woocommerce_integration() {
    if ( class_exists( 'WooCommerce' ) ) {
        require_once RIDCODTOKEN_PLUGIN_DIR . 'includes/woocommerce/integration.php';
    }
}
add_action( 'plugins_loaded', 'ridcodtoken_load_woocommerce_integration' );

// --- Deactivation Hook ---
/**
 * Clears the scheduled license check event upon plugin deactivation.
 */
function ridcodtoken_deactivate() {
	// Find the next scheduled event
	$timestamp = wp_next_scheduled( RIDCODTOKEN_CRON_HOOK );
	// If scheduled, clear it
	if ( $timestamp ) {
		wp_unschedule_event( $timestamp, RIDCODTOKEN_CRON_HOOK );
	}
}
register_deactivation_hook( RIDCODTOKEN_PLUGIN_FILE, 'ridcodtoken_deactivate' );

// Load Admin specific files
if ( is_admin() ) {
    require_once RIDCODTOKEN_PLUGIN_DIR . 'admin/admin-menu.php';

    // تحميل مركز التراخيص الموحد
    require_once RIDCODTOKEN_PLUGIN_DIR . 'includes/license-center/unified-center.php';

    // List Table class included via admin-menu.php or load hook
}


// --- Load the Core Protected Plugin (RIDCODE) ---
/**
 * Loads the main functionality from the RIDCODE directory,
 * but only if the license is active.
 */
function ridcodtoken_load_core_plugin() {
    // Check if the license is active
    if ( ridcodtoken_is_license_active() ) {
        // Define path to the main file of the protected plugin
        $core_plugin_file = RIDCODTOKEN_PLUGIN_DIR . 'RIDCODE/rid-cod-plugin.php'; // Adjust filename if needed

        if ( file_exists( $core_plugin_file ) ) {
            require_once $core_plugin_file;
            // Call the initialization function from the included file
            if ( function_exists( 'rid_cod_init' ) ) {
                 rid_cod_init();
            }
            // Optional: Instantiate the main class or run an initialization function from rid-cod-plugin.php if necessary
            // Example: if ( class_exists( 'Rid_Cod_Loader' ) ) { Rid_Cod_Loader::get_instance()->run(); }
        } else {
            // Optional: Log an error if the core file is missing despite active license
            error_log("RIDCODTOKEN Error: Core plugin file missing at " . $core_plugin_file . " despite active license.");
            // You might want to show an admin notice here as well
            add_action( 'admin_notices', function() {
                echo '<div class="notice notice-error"><p><strong>RIDCODTOKEN Error:</strong> Core plugin file (RIDCODE/rid-cod-plugin.php) is missing. Please reinstall the plugin.</p></div>';
            });
        }
    }
    // If the license is not active, the core plugin file is simply not included,
    // effectively disabling its functionality. The admin notice should prompt for activation.
}
// Hook the loading process. 'plugins_loaded' is a common hook for this.
// Use a priority slightly later than default (e.g., 20) to ensure checks run after other initializations.
add_action( 'plugins_loaded', 'ridcodtoken_load_core_plugin', 20 );

// Load Elementor Integration (conditionally)
// The integration file itself checks if Elementor is loaded before registering widgets
require_once RIDCODTOKEN_PLUGIN_DIR . 'includes/elementor/elementor-integration.php';

/**
 * Checks if the RIDCODTOKEN license is currently active.
 *
 * @return bool True if active, false otherwise.
 */
function ridcodtoken_is_license_active() {
    $status = get_option( 'ridcodtoken_license_status', 'inactive' );
    $key    = get_option( 'ridcodtoken_activated_key', '' );
    // Consider adding a transient check against the API periodically for more robustness
    return ( $status === 'active' && ! empty( $key ) );
}



// Activation hook
// Points to the function defined in includes/database/activation.php
register_activation_hook( RIDCODTOKEN_PLUGIN_FILE, 'ridcodtoken_activate' );

// Register REST API routes
// Points to the function defined in includes/rest-api/routes.php
add_action( 'rest_api_init', 'ridcodtoken_register_rest_routes' );

// --- Optional: Deactivation Hook ---
// function ridcodtoken_deactivate() {
	// Code to run on deactivation (e.g., cleanup options)
// }
// register_deactivation_hook( RIDCODTOKEN_PLUGIN_FILE, 'ridcodtoken_deactivate' );

// --- Optional: Plugin Initialization ---
// function run_ridcodtoken() {
	// Initialize other plugin components if needed
// }
// add_action( 'plugins_loaded', 'run_ridcodtoken' );

?>