<?php
/**
 * Utility Functions for RIDCODTOKEN
 *
 * @package RIDCODTOKEN
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Generates a unique license key.
 * Tries a few times to ensure uniqueness.
 *
 * @return string|false Unique license key or false on failure.
 */
function ridcodtoken_create_unique_license_key() {
	global $wpdb;
	$table_licenses = $wpdb->prefix . 'ridcod_licenses';
	$max_tries = 5;
	$try = 0;

	do {
		// Generate a potential key (e.g., UUID v4)
		$key = wp_generate_uuid4();

		// Check if it already exists
		$exists = $wpdb->get_var( $wpdb->prepare(
			"SELECT id FROM $table_licenses WHERE license_key = %s",
			$key
		) );

		if ( ! $exists ) {
			return $key; // Found a unique key
		}

		$try++;
	} while ( $try < $max_tries );

	return false; // Failed to find a unique key after several tries
}

// Add other utility functions here if needed in the future.