(function($) {
    'use strict';

    /**
     * RIDCODTOKEN Elementor Activation Widget Script
     */
    var RidcodtokenActivateWidgetHandler = function($scope, $) {
        var $widget = $scope.find('.ridcodtoken-activation-widget');
        if ($widget.length === 0) {
            return; // Widget not found or already active state rendered
        }

        var $button = $widget.find('.ridcodtoken-activate-button');
        var $input = $widget.find('.ridcodtoken-license-key-input');
        var $messageArea = $widget.find('.ridcodtoken-message-area');
        var $messageText = $widget.find('.ridcodtoken-message-text');
        var $spinner = $widget.find('.ridcodtoken-spinner');
        var $nonceField = $widget.find('#_ridcodtoken_save_nonce'); // Nonce for saving

        $button.on('click', function(e) {
            e.preventDefault();
            var licenseKey = $input.val().trim();

            // Basic validation
            if (licenseKey === '') {
                displayMessage(ridcodtoken_activate_params.text_error_invalid_key, 'error');
                return;
            }

            // Disable button and show spinner
            $button.prop('disabled', true);
            $spinner.show();
            displayMessage(ridcodtoken_activate_params.text_activating, 'loading');

            // --- Call REST API to Activate ---
            fetch(ridcodtoken_activate_params.rest_url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': ridcodtoken_activate_params.nonce // REST API Nonce
                },
                body: JSON.stringify({
                    license_key: licenseKey,
                    site_url: ridcodtoken_activate_params.site_url
                })
            })
            .then(response => {
                // Check if response status is OK, otherwise parse error from body
                if (!response.ok) {
                    return response.json().then(errData => {
                        // Throw an error object that includes the message from the API
                        let error = new Error(errData.message || ridcodtoken_activate_params.text_error_general);
                        error.code = errData.code || 'fetch_error';
                        throw error;
                    });
                }
                return response.json();
            })
            .then(data => {
                // Handle successful REST API response (activated or already active)
                if (data.success && (data.code === 'activated' || data.code === 'already_active')) {
                    displayMessage(data.message, 'success');

                    // --- Call WordPress AJAX to Save Key ---
                    // We only save if the API confirms it's valid (activated or already_active)
                    saveLicenseKey(licenseKey);

                    // Optionally hide the form or change state after successful activation
                    // $widget.find('.ridcodtoken-form-container').hide();

                } else {
                    // Handle cases where success might be true but code indicates an issue handled as success (e.g., already active reported by API)
                    // Or if success is false in the response body
                    displayMessage(data.message || ridcodtoken_activate_params.text_error_general, 'error');
                    resetForm();
                }
            })
            .catch(error => {
                // Handle fetch errors or errors thrown from response.json()
                console.error('Activation Error:', error);
                displayMessage(error.message || ridcodtoken_activate_params.text_error_general, 'error');
                resetForm();
            });
        });

        /**
         * Saves the license key via WordPress AJAX.
         */
        function saveLicenseKey(keyToSave) {
            var saveNonce = $nonceField.val(); // Get nonce value from the hidden field

            $.ajax({
                url: ridcodtoken_activate_params.ajax_url,
                type: 'POST',
                data: {
                    action: ridcodtoken_activate_params.save_action, // 'ridcodtoken_save_license'
                    license_key: keyToSave,
                    _ajax_nonce: saveNonce // Use the nonce from the hidden field
                },
                success: function(response) {
                    if (response.success) {
                        // Key saved successfully. Message already shown by REST API success.
                        console.log('License key saved successfully.');
                        // Optionally redirect or update UI further
                        // For example, replace the form with a success message permanently
                         $widget.html('<div class="ridcodtoken-already-active"><p>' + response.data.message + '</p></div>');
                    } else {
                        // Saving failed (nonce invalid, permission denied, etc.)
                        console.error('Failed to save license key:', response.data.message);
                        // Display a more specific error about saving if needed
                        displayMessage(response.data.message || 'Failed to save license key.', 'error');
                        resetForm(); // Allow user to try again? Or maybe not if activation worked...
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    // AJAX request itself failed
                    console.error('AJAX Error:', textStatus, errorThrown);
                    displayMessage('Error communicating with server to save key.', 'error');
                    resetForm();
                }
            });
        }

        /**
         * Displays messages to the user.
         */
        function displayMessage(message, type) {
            $messageText.text(message);
            $messageArea.removeClass('success error loading').addClass(type).show();
            if (type !== 'loading') {
                $spinner.hide();
            }
        }

        /**
         * Resets the form state (enable button, clear message).
         */
        function resetForm() {
            $button.prop('disabled', false);
            $spinner.hide();
            // Optionally clear the input: $input.val('');
            // Optionally hide message after a delay:
            // setTimeout(function() { $messageArea.hide(); }, 5000);
        }
    };

    // Make sure you run this code under Elementor.
    $(window).on('elementor/frontend/init', function() {
        elementorFrontend.hooks.addAction('frontend/element_ready/ridcodtoken-activate.default', RidcodtokenActivateWidgetHandler);
    });

})(jQuery);