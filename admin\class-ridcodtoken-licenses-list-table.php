<?php
/**
 * WP_List_Table class for displaying RIDCODTOKEN Licenses.
 *
 * @package RIDCODTOKEN
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

// Load WP_List_Table if not loaded
if ( ! class_exists( 'WP_List_Table' ) ) {
	require_once ABSPATH . 'wp-admin/includes/class-wp-list-table.php';
}

class RIDCODTOKEN_Licenses_List_Table extends WP_List_Table {

	/** Class constructor */
	public function __construct() {
		parent::__construct( [
			'singular' => __( 'License', 'ridcodtoken' ), // singular name of the listed records
			'plural'   => __( 'Licenses', 'ridcodtoken' ), // plural name of the listed records
			'ajax'     => false, // should this table support ajax?
		] );
	}	/**
	 * Retrieve licenses data from the database
	 *
	 * @param int $per_page
	 * @param int $page_number
	 *
	 * @return mixed
	 */
	public static function get_licenses( $per_page = 10, $page_number = 1 ) {
		global $wpdb;

		$table_licenses = $wpdb->prefix . 'ridcod_licenses';
		$table_sites    = $wpdb->prefix . 'ridcod_license_sites';

		$sql = "SELECT l.*, COUNT(s.id) as activated_count
                FROM {$table_licenses} l
                LEFT JOIN {$table_sites} s ON l.id = s.license_id";

		$where_clauses = array();

        // Handle Search
        if ( ! empty( $_REQUEST['s'] ) ) {
            $search_term = '%' . $wpdb->esc_like( sanitize_text_field( $_REQUEST['s'] ) ) . '%';
			$search_type = isset( $_REQUEST['search_type'] ) ? sanitize_text_field( $_REQUEST['search_type'] ) : '';
			
			switch ( $search_type ) {
				case 'email':
					$where_clauses[] = $wpdb->prepare( "l.user_email LIKE %s", $search_term );
					break;
				case 'license_key':
					$where_clauses[] = $wpdb->prepare( "l.license_key LIKE %s", $search_term );
					break;
				default:
					$where_clauses[] = $wpdb->prepare( "(l.license_key LIKE %s OR l.user_email LIKE %s)", $search_term, $search_term );
					break;
			}
        }

		// Handle Status Filter
		if ( ! empty( $_REQUEST['status_filter'] ) ) {
			$status_filter = sanitize_text_field( $_REQUEST['status_filter'] );
			if ( in_array( $status_filter, array( 'active', 'inactive' ) ) ) {
				$where_clauses[] = $wpdb->prepare( "l.status = %s", $status_filter );
			}
		}

		// Handle Expiry Filter
		if ( ! empty( $_REQUEST['expiry_filter'] ) ) {
			$expiry_filter = sanitize_text_field( $_REQUEST['expiry_filter'] );
			$current_time = current_time( 'mysql', 1 ); // GMT time
			$thirty_days_later = date( 'Y-m-d H:i:s', strtotime( '+30 days', strtotime( $current_time ) ) );
			
			switch ( $expiry_filter ) {
				case 'expired':
					$where_clauses[] = $wpdb->prepare( "l.expires_at IS NOT NULL AND l.expires_at < %s", $current_time );
					break;
				case 'expiring_soon':
					$where_clauses[] = $wpdb->prepare( "l.expires_at IS NOT NULL AND l.expires_at >= %s AND l.expires_at <= %s", $current_time, $thirty_days_later );
					break;
				case 'lifetime':
					$where_clauses[] = "l.expires_at IS NULL";
					break;
			}
		}

		// Add WHERE clause if we have conditions
		if ( ! empty( $where_clauses ) ) {
			$sql .= ' WHERE ' . implode( ' AND ', $where_clauses );
		}

        $sql .= " GROUP BY l.id";

		// Handle Sorting
		if ( ! empty( $_REQUEST['orderby'] ) ) {
			$orderby = sanitize_sql_orderby( $_REQUEST['orderby'] );
            // Whitelist columns to prevent SQL injection
            $allowed_orderby = ['id', 'license_key', 'user_email', 'status', 'sites_allowed', 'created_at', 'expires_at'];
            if (in_array($orderby, $allowed_orderby)) {
                 $sql .= ' ORDER BY l.' . $orderby;
                 $sql .= ! empty( $_REQUEST['order'] ) ? ' ' . esc_sql( strtoupper( $_REQUEST['order'] ) ) : ' ASC';
            } else {
                $sql .= ' ORDER BY l.id DESC'; // Default sort
            }
		} else {
			$sql .= ' ORDER BY l.id DESC'; // Default sort
		}

		$sql .= " LIMIT $per_page";
		$sql .= ' OFFSET ' . ( $page_number - 1 ) * $per_page;

		$result = $wpdb->get_results( $sql, 'ARRAY_A' );

		return $result;
	}	/**
	 * Returns the count of records in the database.
	 *
	 * @return null|string
	 */
	public static function record_count() {
		global $wpdb;
		$table_licenses = $wpdb->prefix . 'ridcod_licenses';

        $sql = "SELECT COUNT(*) FROM {$table_licenses}";

		$where_clauses = array();

        // Handle Search for count
        if ( ! empty( $_REQUEST['s'] ) ) {
            $search_term = '%' . $wpdb->esc_like( sanitize_text_field( $_REQUEST['s'] ) ) . '%';
			$search_type = isset( $_REQUEST['search_type'] ) ? sanitize_text_field( $_REQUEST['search_type'] ) : '';
			
			switch ( $search_type ) {
				case 'email':
					$where_clauses[] = $wpdb->prepare( "user_email LIKE %s", $search_term );
					break;
				case 'license_key':
					$where_clauses[] = $wpdb->prepare( "license_key LIKE %s", $search_term );
					break;
				default:
					$where_clauses[] = $wpdb->prepare( "(license_key LIKE %s OR user_email LIKE %s)", $search_term, $search_term );
					break;
			}
        }

		// Handle Status Filter for count
		if ( ! empty( $_REQUEST['status_filter'] ) ) {
			$status_filter = sanitize_text_field( $_REQUEST['status_filter'] );
			if ( in_array( $status_filter, array( 'active', 'inactive' ) ) ) {
				$where_clauses[] = $wpdb->prepare( "status = %s", $status_filter );
			}
		}

		// Handle Expiry Filter for count
		if ( ! empty( $_REQUEST['expiry_filter'] ) ) {
			$expiry_filter = sanitize_text_field( $_REQUEST['expiry_filter'] );
			$current_time = current_time( 'mysql', 1 ); // GMT time
			$thirty_days_later = date( 'Y-m-d H:i:s', strtotime( '+30 days', strtotime( $current_time ) ) );
			
			switch ( $expiry_filter ) {
				case 'expired':
					$where_clauses[] = $wpdb->prepare( "expires_at IS NOT NULL AND expires_at < %s", $current_time );
					break;
				case 'expiring_soon':
					$where_clauses[] = $wpdb->prepare( "expires_at IS NOT NULL AND expires_at >= %s AND expires_at <= %s", $current_time, $thirty_days_later );
					break;
				case 'lifetime':
					$where_clauses[] = "expires_at IS NULL";
					break;
			}
		}

		// Add WHERE clause if we have conditions
		if ( ! empty( $where_clauses ) ) {
			$sql .= ' WHERE ' . implode( ' AND ', $where_clauses );
		}

		return $wpdb->get_var( $sql );
	}
	/** Text displayed when no license data is available */
	public function no_items() {
		$search_type = isset( $_REQUEST['search_type'] ) ? sanitize_text_field( $_REQUEST['search_type'] ) : '';
		$search_term = isset( $_REQUEST['s'] ) ? sanitize_text_field( $_REQUEST['s'] ) : '';
		
		if ( $search_type === 'email' && !empty( $search_term ) ) {
			printf( 
				__( 'No licenses found for email address: <strong>%s</strong>', 'ridcodtoken' ), 
				esc_html( $search_term ) 
			);
		} elseif ( !empty( $search_term ) ) {
			printf( 
				__( 'No licenses found matching: <strong>%s</strong>', 'ridcodtoken' ), 
				esc_html( $search_term ) 
			);
		} else {
			_e( 'No licenses found.', 'ridcodtoken' );
		}
	}
	/**
	 * Render a column when no column specific method exists.
	 *
	 * @param array $item
	 * @param string $column_name
	 *
	 * @return mixed
	 */
	public function column_default( $item, $column_name ) {
		switch ( $column_name ) {
			case 'user_email':
				return '<strong>' . esc_html( $item[ $column_name ] ) . '</strong>';
			case 'status':
				$status = $item[ $column_name ];
				$class = 'ridcodtoken-status-' . $status;
				$text = ( $status === 'active' ) ? __( 'Active', 'ridcodtoken' ) : __( 'Inactive', 'ridcodtoken' );
				return '<span class="' . esc_attr( $class ) . '">' . esc_html( $text ) . '</span>';
            case 'sites_allowed':
				return '<span class="badge">' . esc_html( $item[ $column_name ] ) . '</span>';
			case 'created_at':
			case 'expires_at':
				if ( ! empty( $item[ $column_name ] ) ) {
					$date = new DateTime( $item[ $column_name ] );
					return '<span title="' . esc_attr( $date->format( 'Y-m-d H:i:s' ) ) . '">' . 
					       esc_html( $date->format( 'd/m/Y' ) ) . '</span>';
				}
				return '—';
            case 'sites_activated':
                // Display activated count / allowed count
                $activated = isset($item['activated_count']) ? $item['activated_count'] : 0;
                $allowed = $item['sites_allowed'];
				$percentage = $allowed > 0 ? round( ( $activated / $allowed ) * 100 ) : 0;
				$class = $percentage >= 100 ? 'full' : ( $percentage >= 80 ? 'high' : 'normal' );
                return sprintf( '<span class="usage-%s" title="%d%%">%d / %d</span>', 
					esc_attr( $class ), $percentage, $activated, $allowed );
			default:
				return esc_html( $item[ $column_name ] ? $item[ $column_name ] : '—' );
		}
	}

	/**
	 * Render the bulk edit checkbox
	 *
	 * @param array $item
	 *
	 * @return string
	 */
	function column_cb( $item ) {
		return sprintf(
			'<input type="checkbox" name="license[]" value="%s" />', $item['id']
		);
	}

	/**
	 * Method for name column
	 *
	 * @param array $item an array of DB data
	 *
	 * @return string
	 */
	function column_license_key( $item ) {
		$nonce_delete = wp_create_nonce( 'ridcodtoken_delete_' . $item['id'] );
        $nonce_activate = wp_create_nonce( 'ridcodtoken_activate_' . $item['id'] );
        $nonce_deactivate = wp_create_nonce( 'ridcodtoken_deactivate_' . $item['id'] );

		$title = '<strong>' . esc_html( $item['license_key'] ) . '</strong>';

		$actions = array();
        // Edit Action - Placeholder for now
        // $actions['edit'] = sprintf( '<a href="?page=%s&action=%s&license=%s">Edit</a>', esc_attr( $_REQUEST['page'] ), 'edit', absint( $item['id'] ) );

        if ($item['status'] === 'active') {
             $actions['deactivate'] = sprintf( '<a href="?page=%s&action=%s&license=%s&_wpnonce=%s">Deactivate</a>', esc_attr( $_REQUEST['page'] ), 'deactivate', absint( $item['id'] ), $nonce_deactivate );
        } else {
             $actions['activate'] = sprintf( '<a href="?page=%s&action=%s&license=%s&_wpnonce=%s">Activate</a>', esc_attr( $_REQUEST['page'] ), 'activate', absint( $item['id'] ), $nonce_activate );
        }

		$actions['delete'] = sprintf(
            '<a href="?page=%s&action=%s&license=%s&_wpnonce=%s" onclick="return confirm(\'Are you sure you want to delete this license? This cannot be undone.\')">Delete</a>',
            esc_attr( $_REQUEST['page'] ),
            'delete',
            absint( $item['id'] ),
            $nonce_delete
        );


		return $title . $this->row_actions( $actions );
	}

	/**
	 *  Associative array of columns
	 *
	 * @return array
	 */
	function get_columns() {
		$columns = [
			'cb'            => '<input type="checkbox" />',
			'license_key'   => __( 'License Key', 'ridcodtoken' ),
			'user_email'    => __( 'User Email', 'ridcodtoken' ),
			'status'        => __( 'Status', 'ridcodtoken' ),
			'sites_allowed' => __( 'Sites Allowed', 'ridcodtoken' ),
            'sites_activated' => __( 'Sites Activated', 'ridcodtoken' ),
			'created_at'    => __( 'Created At', 'ridcodtoken' ),
            'expires_at'    => __( 'Expires At', 'ridcodtoken' ),
		];

		return $columns;
	}

	/**
	 * Columns to make sortable.
	 *
	 * @return array
	 */
	public function get_sortable_columns() {
		$sortable_columns = array(
			'license_key'   => array( 'license_key', true ), // true means it's already sorted
			'user_email'    => array( 'user_email', false ),
			'status'        => array( 'status', false ),
            'sites_allowed' => array( 'sites_allowed', false ),
			'created_at'    => array( 'created_at', false ),
            'expires_at'    => array( 'expires_at', false ),
		);

		return $sortable_columns;
	}

	/**
	 * Returns an associative array containing the bulk action
	 *
	 * @return array
	 */
	// public function get_bulk_actions() {
	// 	$actions = [
	// 		'bulk-delete' => 'Delete'
    //      // Add activate/deactivate later if needed
	// 	];
	// 	return $actions;
	// }
	/**
	 * Handles data query and filter, sorting, and pagination.
	 */
	public function prepare_items() {
        $this->_column_headers = $this->get_column_info();

		/** Process bulk action */
		// $this->process_bulk_action(); // Handled in admin-menu.php for now

		$per_page     = $this->get_items_per_page( 'licenses_per_page', 10 ); // Changed from 20 to 10
		$current_page = $this->get_pagenum();
		$total_items  = self::record_count();

		$this->set_pagination_args( [
			'total_items' => $total_items, // WE have to calculate the total number of items
			'per_page'    => $per_page, // WE have to determine how many items to show on a page
            'total_pages' => ceil( $total_items / $per_page ) // WE have to calculate the total number of pages
		] );

		$this->items = self::get_licenses( $per_page, $current_page );
	}	/**
	 * Display the table filters (dropdown for status, search)
	 */
	protected function extra_tablenav( $which ) {
		if ( 'top' === $which ) {
			$current_status = isset( $_REQUEST['status_filter'] ) ? sanitize_text_field( $_REQUEST['status_filter'] ) : '';
			$current_search_type = isset( $_REQUEST['search_type'] ) ? sanitize_text_field( $_REQUEST['search_type'] ) : '';
			$current_expiry_filter = isset( $_REQUEST['expiry_filter'] ) ? sanitize_text_field( $_REQUEST['expiry_filter'] ) : '';
			$current_search = isset( $_REQUEST['s'] ) ? sanitize_text_field( $_REQUEST['s'] ) : '';
			?>			<!-- Quick Email Search Section -->
			<div class="ridcodtoken-quick-email-search">
				<h4><span class="dashicons dashicons-email"></span> <?php _e( 'Quick Email Search', 'ridcodtoken' ); ?> <small>(<?php _e( 'Press Ctrl+E to focus', 'ridcodtoken' ); ?>)</small></h4>
				<p><?php _e( 'Search for all licenses belonging to a specific email address:', 'ridcodtoken' ); ?></p><div class="email-search-row">
					<input type="email" name="quick_email_search" id="quick_email_search" 
						   value="<?php echo ($current_search_type === 'email') ? esc_attr($current_search) : ''; ?>" 
						   placeholder="<?php _e( 'Enter email address...', 'ridcodtoken' ); ?>" 
						   class="regular-text" 
						   title="<?php _e( 'Enter a valid email address to search for all associated licenses', 'ridcodtoken' ); ?>" />
					<button type="button" onclick="ridcodtokenQuickEmailSearch()" class="button button-primary"
							title="<?php _e( 'Search for all licenses belonging to this email', 'ridcodtoken' ); ?>">
						<span class="dashicons dashicons-search"></span> <?php _e( 'Search Email', 'ridcodtoken' ); ?>
					</button><?php if ($current_search_type === 'email' && !empty($current_search)): ?>
						<?php $email_count = $this->record_count(); ?>
						<a href="<?php echo esc_url( admin_url( 'admin.php?page=' . $_REQUEST['page'] ) ); ?>" class="button">
							<span class="dashicons dashicons-dismiss"></span> <?php _e( 'Clear', 'ridcodtoken' ); ?>
						</a>
						<span class="search-result-indicator">
							<span class="dashicons dashicons-yes"></span> 
							<?php printf( __( 'Found %d license(s) for: %s', 'ridcodtoken' ), $email_count, '<strong>' . esc_html($current_search) . '</strong>' ); ?>
						</span>
					<?php endif; ?>
				</div>
			</div>

			<!-- Advanced Filters Section -->
			<div class="alignleft actions">
				<select name="status_filter" id="status_filter">
					<option value=""><?php _e( 'All Statuses', 'ridcodtoken' ); ?></option>
					<option value="active" <?php selected( $current_status, 'active' ); ?>><?php _e( 'Active', 'ridcodtoken' ); ?></option>
					<option value="inactive" <?php selected( $current_status, 'inactive' ); ?>><?php _e( 'Inactive', 'ridcodtoken' ); ?></option>
				</select>
				
				<select name="expiry_filter" id="expiry_filter">
					<option value=""><?php _e( 'All Licenses', 'ridcodtoken' ); ?></option>
					<option value="expired" <?php selected( $current_expiry_filter, 'expired' ); ?>><?php _e( 'Expired', 'ridcodtoken' ); ?></option>
					<option value="expiring_soon" <?php selected( $current_expiry_filter, 'expiring_soon' ); ?>><?php _e( 'Expiring in 30 days', 'ridcodtoken' ); ?></option>
					<option value="lifetime" <?php selected( $current_expiry_filter, 'lifetime' ); ?>><?php _e( 'Lifetime', 'ridcodtoken' ); ?></option>
				</select>
				
				<select name="search_type" id="search_type">
					<option value=""><?php _e( 'Search in all fields', 'ridcodtoken' ); ?></option>
					<option value="email" <?php selected( $current_search_type, 'email' ); ?>><?php _e( 'Search by Email only', 'ridcodtoken' ); ?></option>
					<option value="license_key" <?php selected( $current_search_type, 'license_key' ); ?>><?php _e( 'Search by License Key only', 'ridcodtoken' ); ?></option>
				</select>
				
				<?php submit_button( __( 'Filter', 'ridcodtoken' ), 'button', 'filter_action', false ); ?>
				
				<?php if ( ! empty( $_REQUEST['s'] ) || ! empty( $_REQUEST['status_filter'] ) || ! empty( $_REQUEST['expiry_filter'] ) ): ?>
					<a href="<?php echo esc_url( admin_url( 'admin.php?page=' . $_REQUEST['page'] ) ); ?>" class="button"><?php _e( 'Clear Filters', 'ridcodtoken' ); ?></a>
				<?php endif; ?>
			</div>			<script>
			function ridcodtokenQuickEmailSearch() {
				const emailInput = document.getElementById('quick_email_search');
				const searchButton = emailInput.nextElementSibling;
				const email = emailInput.value.trim();
				
				if (!email) {
					alert('<?php _e( 'Please enter an email address', 'ridcodtoken' ); ?>');
					emailInput.focus();
					return;
				}
				
				// Validate email format
				const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
				if (!emailRegex.test(email)) {
					alert('<?php _e( 'Please enter a valid email address', 'ridcodtoken' ); ?>');
					emailInput.focus();
					return;
				}
				
				// Show loading state
				const originalText = searchButton.innerHTML;
				searchButton.innerHTML = '<span class="dashicons dashicons-update"></span> <?php _e( 'Searching...', 'ridcodtoken' ); ?>';
				searchButton.disabled = true;
				
				// Build the search URL
				const currentUrl = new URL(window.location.href);
				currentUrl.searchParams.set('s', email);
				currentUrl.searchParams.set('search_type', 'email');
				currentUrl.searchParams.delete('status_filter');
				currentUrl.searchParams.delete('expiry_filter');
				currentUrl.searchParams.delete('paged');
				
				// Navigate to the search
				window.location.href = currentUrl.toString();
			}
					// Allow Enter key to trigger search
			document.addEventListener('DOMContentLoaded', function() {
				const emailInput = document.getElementById('quick_email_search');
				if (emailInput) {
					emailInput.addEventListener('keypress', function(e) {
						if (e.key === 'Enter') {
							e.preventDefault();
							ridcodtokenQuickEmailSearch();
						}
					});
					
					// Auto-focus on email input if it has value
					if (emailInput.value) {
						emailInput.focus();
					}
				}
				
				// Add keyboard shortcut Ctrl+E to focus email search
				document.addEventListener('keydown', function(e) {
					if (e.ctrlKey && e.key === 'e') {
						e.preventDefault();
						if (emailInput) {
							emailInput.focus();
							emailInput.select();
						}
					}
				});
			});
			</script>
			<?php
		}
	}

	/**
	 * Display enhanced search box with placeholder
	 */
	public function search_box( $text, $input_id ) {
		$search_type = isset( $_REQUEST['search_type'] ) ? sanitize_text_field( $_REQUEST['search_type'] ) : '';
		$placeholder = '';
		
		switch ( $search_type ) {
			case 'email':
				$placeholder = __( 'Search by email...', 'ridcodtoken' );
				break;
			case 'license_key':
				$placeholder = __( 'Search by license key...', 'ridcodtoken' );
				break;
			default:
				$placeholder = __( 'Search licenses...', 'ridcodtoken' );
				break;
		}

		if ( empty( $_REQUEST['s'] ) && ! $this->has_items() ) {
			return;
		}

		$input_id = $input_id . '-search-input';

		if ( ! empty( $_REQUEST['orderby'] ) ) {
			echo '<input type="hidden" name="orderby" value="' . esc_attr( $_REQUEST['orderby'] ) . '" />';
		}
		if ( ! empty( $_REQUEST['order'] ) ) {
			echo '<input type="hidden" name="order" value="' . esc_attr( $_REQUEST['order'] ) . '" />';
		}
		if ( ! empty( $_REQUEST['status_filter'] ) ) {
			echo '<input type="hidden" name="status_filter" value="' . esc_attr( $_REQUEST['status_filter'] ) . '" />';
		}
		if ( ! empty( $_REQUEST['search_type'] ) ) {
			echo '<input type="hidden" name="search_type" value="' . esc_attr( $_REQUEST['search_type'] ) . '" />';
		}
		if ( ! empty( $_REQUEST['expiry_filter'] ) ) {
			echo '<input type="hidden" name="expiry_filter" value="' . esc_attr( $_REQUEST['expiry_filter'] ) . '" />';
		}
		?>
		<p class="search-box">
			<label class="screen-reader-text" for="<?php echo esc_attr( $input_id ); ?>"><?php echo $text; ?>:</label>
			<input type="search" id="<?php echo esc_attr( $input_id ); ?>" name="s" value="<?php _admin_search_query(); ?>" placeholder="<?php echo esc_attr( $placeholder ); ?>" />
			<?php submit_button( $text, '', '', false, array( 'id' => 'search-submit' ) ); ?>
		</p>
		<?php
	}

    // process_bulk_action() method would go here if handling bulk actions within this class
}