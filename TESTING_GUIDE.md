# دليل اختبار النظام الجديد - ربط التراخيص بالمنتجات

## 🎯 الهدف من الاختبار
التأكد من أن النظام الجديد يمنع استخدام ترخيص واحد لمنتجات متعددة بنجاح.

---

## 📋 السيناريوهات المطلوب اختبارها

### السيناريو 1: إنشاء ترخيص يدوي
**الخطوات:**
1. اذهب إلى License Manager > Add New
2. أدخل البيانات التالية:
   - User Email: <EMAIL>
   - Status: Active
   - Sites Allowed: 1
   - **Product ID: 123** (مطلوب الآن)
   - اترك License Key فارغ للتوليد التلقائي
3. اضغط "Add License"

**النتيجة المتوقعة:**
- ✅ يتم إنشاء الترخيص بنجاح
- ✅ يظهر Product ID في قائمة التراخيص
- ✅ الترخيص مرتبط بالمنتج 123 فقط

### السيناريو 2: اختبار SDK مع المنتج الصحيح
**الخطوات:**
1. أنشئ إضافة اختبار بالكود التالي:

```php
<?php
/**
 * Plugin Name: اختبار المنتج 123
 */

require_once plugin_dir_path(__FILE__) . 'plugin-sdk/ridcodtoken-plugin-sdk.php';

$license_sdk = ridcodtoken_init_plugin_sdk(array(
    'plugin_file' => __FILE__,
    'plugin_name' => 'اختبار المنتج 123',
    'api_url' => 'https://yoursite.com',
    'plugin_slug' => 'test-product-123',
    'product_id' => 123, // نفس المنتج
    'version' => '1.0.0'
));

if (!$license_sdk->is_licensed()) {
    return; // لن تعمل الإضافة
}

// كود الإضافة هنا
add_action('admin_notices', function() {
    echo '<div class="notice notice-success"><p>✅ المنتج 123 يعمل بنجاح!</p></div>';
});
?>
```

2. فعل الإضافة
3. أدخل الترخيص المُنشأ في السيناريو 1

**النتيجة المتوقعة:**
- ✅ يتم قبول الترخيص
- ✅ تظهر رسالة "المنتج 123 يعمل بنجاح!"

### السيناريو 3: اختبار SDK مع منتج مختلف (يجب أن يفشل)
**الخطوات:**
1. أنشئ إضافة اختبار أخرى بالكود التالي:

```php
<?php
/**
 * Plugin Name: اختبار المنتج 456
 */

require_once plugin_dir_path(__FILE__) . 'plugin-sdk/ridcodtoken-plugin-sdk.php';

$license_sdk = ridcodtoken_init_plugin_sdk(array(
    'plugin_file' => __FILE__,
    'plugin_name' => 'اختبار المنتج 456',
    'api_url' => 'https://yoursite.com',
    'plugin_slug' => 'test-product-456',
    'product_id' => 456, // منتج مختلف
    'version' => '1.0.0'
));

if (!$license_sdk->is_licensed()) {
    return; // لن تعمل الإضافة
}

// كود الإضافة هنا
add_action('admin_notices', function() {
    echo '<div class="notice notice-success"><p>✅ المنتج 456 يعمل بنجاح!</p></div>';
});
?>
```

2. فعل الإضافة
3. حاول استخدام نفس الترخيص من السيناريو 1

**النتيجة المتوقعة:**
- ❌ يتم رفض الترخيص
- ❌ تظهر صفحة تفعيل الترخيص
- ❌ رسالة خطأ: "This license key is not valid for this product"

### السيناريو 4: اختبار WooCommerce التلقائي
**الخطوات:**
1. أنشئ منتج في WooCommerce برقم 789
2. أنشئ خطة ترخيص جديدة:
   - Plan Name: "خطة المنتج 789"
   - Product ID: 789
   - Sites Allowed: 1
   - Duration: 365 days
3. اشتر المنتج عبر WooCommerce
4. تأكد من إنشاء الترخيص تلقائياً

**النتيجة المتوقعة:**
- ✅ يتم إنشاء ترخيص مرتبط بالمنتج 789
- ✅ الترخيص يعمل فقط مع إضافات المنتج 789

---

## 🔍 نقاط التحقق المهمة

### 1. قاعدة البيانات
تحقق من الجداول التالية:

**جدول `wp_ridcod_licenses`:**
```sql
SELECT id, license_key, product_id, status FROM wp_ridcod_licenses;
```
- يجب أن يحتوي على `product_id` لكل ترخيص

**جدول `wp_ridcod_license_sites`:**
```sql
SELECT id, license_id, site_url, product_id FROM wp_ridcod_license_sites;
```
- يجب أن يحتوي على `product_id` لكل تفعيل

**جدول `wp_ridcod_license_products`:**
```sql
SELECT * FROM wp_ridcod_license_products;
```
- جدول جديد لربط التراخيص بمنتجات متعددة (للاستخدام المستقبلي)

### 2. REST API
اختبر endpoints التالية:

**تفعيل الترخيص:**
```bash
curl -X POST "https://yoursite.com/wp-json/ridcodtoken/v1/activate" \
  -H "Content-Type: application/json" \
  -d '{
    "license_key": "your-license-key",
    "site_url": "https://testsite.com",
    "product_id": 123
  }'
```

**فحص حالة الترخيص:**
```bash
curl "https://yoursite.com/wp-json/ridcodtoken/v1/status?license_key=your-license-key&site_url=https://testsite.com&product_id=123"
```

### 3. رسائل الخطأ المتوقعة
- "Product ID is required and must be valid"
- "This license key is not valid for this product"
- "License key not found"

---

## ✅ معايير النجاح

### النظام يعمل بنجاح إذا:
1. **لا يمكن استخدام ترخيص واحد لمنتجات متعددة**
2. **التراخيص اليدوية تتطلب Product ID**
3. **التراخيص التلقائية ترتبط بالمنتج الصحيح**
4. **SDK يرفض التراخيص غير المطابقة للمنتج**
5. **رسائل الخطأ واضحة ومفيدة**

### النظام يفشل إذا:
1. **يمكن استخدام ترخيص واحد لمنتجات متعددة**
2. **لا يتم التحقق من Product ID**
3. **تظهر أخطاء في قاعدة البيانات**
4. **SDK لا يعمل مع التحديثات الجديدة**

---

## 🚨 مشاكل محتملة وحلولها

### مشكلة: "Column 'product_id' doesn't exist"
**الحل:** قم بتشغيل:
```sql
ALTER TABLE wp_ridcod_license_sites ADD COLUMN product_id bigint(20) UNSIGNED DEFAULT NULL AFTER license_id;
```

### مشكلة: التراخيص القديمة لا تعمل
**الحل:** قم بتحديث التراخيص القديمة:
```sql
UPDATE wp_ridcod_licenses SET product_id = 1 WHERE product_id IS NULL;
```

### مشكلة: SDK لا يرسل Product ID
**الحل:** تأكد من إضافة `product_id` في إعدادات SDK

---

## 📊 تقرير الاختبار

بعد إجراء جميع الاختبارات، املأ هذا التقرير:

- [ ] السيناريو 1: إنشاء ترخيص يدوي ✅/❌
- [ ] السيناريو 2: SDK مع المنتج الصحيح ✅/❌  
- [ ] السيناريو 3: SDK مع منتج مختلف ✅/❌
- [ ] السيناريو 4: WooCommerce التلقائي ✅/❌
- [ ] قاعدة البيانات محدثة ✅/❌
- [ ] REST API يعمل ✅/❌
- [ ] رسائل الخطأ صحيحة ✅/❌

**النتيجة النهائية:** ✅ نجح / ❌ فشل

**ملاحظات إضافية:**
_اكتب أي ملاحظات أو مشاكل واجهتها هنا_
