<?php
/**
 * RIDCODTOKEN Plugin SDK
 * نظام حماية الإضافات بالتراخيص
 *
 * @package RIDCODTOKEN_Plugin_SDK
 * @version 1.0.0
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

/**
 * فئة SDK للإضافات
 */
class RIDCODTOKEN_Plugin_SDK {

    /**
     * معلومات الإضافة المحمية
     */
    private $plugin_info = array();

    /**
     * رابط API للتحقق من التراخيص
     */
    private $api_url = '';

    /**
     * مفتاح الترخيص المحفوظ
     */
    private $license_key = '';

    /**
     * حالة الترخيص
     */
    private $license_status = 'inactive';

    /**
     * آخر تحقق من الترخيص
     */
    private $last_check = 0;

    /**
     * فترة التحقق (5 ساعات بالثواني)
     */
    private $check_interval = 18000; // 5 * 60 * 60

    /**
     * البادئة للخيارات في قاعدة البيانات
     */
    private $option_prefix = '';

    /**
     * مسار الإضافة الرئيسي
     */
    private $plugin_file = '';

    /**
     * اسم الإضافة للعرض
     */
    private $plugin_name = '';

    /**
     * Constructor
     *
     * @param array $config إعدادات SDK
     */
    public function __construct($config = array()) {

        // تعيين الإعدادات الافتراضية
        $defaults = array(
            'plugin_file' => '',
            'plugin_name' => 'Protected Plugin',
            'api_url' => '',
            'plugin_slug' => '',
            'version' => '1.0.0'
        );

        $this->plugin_info = wp_parse_args($config, $defaults);

        // تعيين المتغيرات
        $this->plugin_file = $this->plugin_info['plugin_file'];
        $this->plugin_name = $this->plugin_info['plugin_name'];
        $this->api_url = trailingslashit($this->plugin_info['api_url']);
        $this->option_prefix = 'ridcodtoken_' . sanitize_key($this->plugin_info['plugin_slug']) . '_';

        // تحميل حالة الترخيص
        $this->load_license_data();

        // تشغيل النظام
        $this->init();
    }

    /**
     * تهيئة النظام
     */
    private function init() {

        // إضافة قائمة الإدارة دائماً (بغض النظر عن حالة الترخيص)
        add_action('admin_menu', array($this, 'add_license_management_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_ridcodtoken_activate_license', array($this, 'ajax_activate_license'));
        add_action('wp_ajax_ridcodtoken_deactivate_license', array($this, 'ajax_deactivate_license'));
        add_action('wp_ajax_ridcodtoken_check_license_status', array($this, 'ajax_check_license_status'));

        // التحقق من صحة الترخيص
        if (!$this->is_license_valid()) {
            // إذا لم يكن الترخيص صحيح، عرض صفحة التفعيل فقط
            // إخفاء الإضافة من قائمة الإضافات
            add_filter('all_plugins', array($this, 'hide_plugin_from_list'));

            // منع تفعيل وظائف الإضافة
            return;
        }

        // إذا كان الترخيص صحيح، تشغيل التحقق الدوري
        add_action('init', array($this, 'schedule_license_check'));
        add_action('ridcodtoken_check_license_' . $this->plugin_info['plugin_slug'], array($this, 'check_license_status'));
    }

    /**
     * تحميل بيانات الترخيص من قاعدة البيانات
     */
    private function load_license_data() {
        $this->license_key = get_option($this->option_prefix . 'license_key', '');
        $this->license_status = get_option($this->option_prefix . 'license_status', 'inactive');
        $this->last_check = get_option($this->option_prefix . 'last_check', 0);
    }

    /**
     * حفظ بيانات الترخيص
     */
    private function save_license_data() {
        update_option($this->option_prefix . 'license_key', $this->license_key);
        update_option($this->option_prefix . 'license_status', $this->license_status);
        update_option($this->option_prefix . 'last_check', time());
    }

    /**
     * التحقق من صحة الترخيص
     */
    private function is_license_valid() {

        // التحقق من وجود مفتاح الترخيص
        if (empty($this->license_key)) {
            return false;
        }

        // التحقق من حالة الترخيص
        if ($this->license_status !== 'active') {
            return false;
        }

        // التحقق من آخر فحص (إذا مر أكثر من 5 ساعات، نحتاج للتحقق مرة أخرى)
        if ((time() - $this->last_check) > $this->check_interval) {
            return $this->verify_license_with_server();
        }

        return true;
    }

    /**
     * التحقق من الترخيص مع الخادم
     */
    private function verify_license_with_server() {

        if (empty($this->license_key) || empty($this->api_url)) {
            return false;
        }

        $api_endpoint = $this->api_url . 'wp-json/ridcodtoken/v1/status';

        $response = wp_remote_get($api_endpoint . '?' . http_build_query(array(
            'license_key' => $this->license_key,
            'site_url' => home_url()
        )), array(
            'timeout' => 30,
            'headers' => array(
                'User-Agent' => 'RIDCODTOKEN-SDK/' . $this->plugin_info['version']
            )
        ));

        if (is_wp_error($response)) {
            // في حالة فشل الاتصال، نبقي على الحالة الحالية لفترة قصيرة
            $this->last_check = time() - ($this->check_interval - 3600); // إعادة المحاولة خلال ساعة
            update_option($this->option_prefix . 'last_check', $this->last_check);
            return ($this->license_status === 'active');
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!$data) {
            return false;
        }

        // تحديث حالة الترخيص
        $is_valid = false;

        if (isset($data['status']) && $data['status'] === 'active' &&
            isset($data['site_is_active']) && $data['site_is_active'] === true &&
            (!isset($data['is_expired']) || $data['is_expired'] !== true)) {

            $this->license_status = 'active';
            $is_valid = true;
        } else {
            $this->license_status = 'inactive';
        }

        // حفظ البيانات المحدثة
        $this->save_license_data();

        return $is_valid;
    }

    /**
     * تفعيل الترخيص
     */
    private function activate_license($license_key) {

        if (empty($license_key) || empty($this->api_url)) {
            return array('success' => false, 'message' => 'بيانات غير صحيحة');
        }

        $api_endpoint = $this->api_url . 'wp-json/ridcodtoken/v1/activate';

        $response = wp_remote_post($api_endpoint, array(
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/json',
                'User-Agent' => 'RIDCODTOKEN-SDK/' . $this->plugin_info['version']
            ),
            'body' => json_encode(array(
                'license_key' => $license_key,
                'site_url' => home_url()
            ))
        ));

        if (is_wp_error($response)) {
            return array('success' => false, 'message' => 'فشل في الاتصال بالخادم: ' . $response->get_error_message());
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!$data) {
            return array('success' => false, 'message' => 'استجابة غير صحيحة من الخادم');
        }

        if (isset($data['activated']) && $data['activated'] === true) {
            // حفظ الترخيص
            $this->license_key = $license_key;
            $this->license_status = 'active';
            $this->save_license_data();

            return array('success' => true, 'message' => 'تم تفعيل الترخيص بنجاح');
        } else {
            $message = isset($data['message']) ? $data['message'] : 'فشل في تفعيل الترخيص';
            return array('success' => false, 'message' => $message);
        }
    }

    /**
     * إلغاء تفعيل الترخيص
     */
    private function deactivate_license() {

        if (empty($this->license_key) || empty($this->api_url)) {
            return array('success' => false, 'message' => 'لا يوجد ترخيص مفعل');
        }

        $api_endpoint = $this->api_url . 'wp-json/ridcodtoken/v1/deactivate';

        $response = wp_remote_post($api_endpoint, array(
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/json',
                'User-Agent' => 'RIDCODTOKEN-SDK/' . $this->plugin_info['version']
            ),
            'body' => json_encode(array(
                'license_key' => $this->license_key,
                'site_url' => home_url()
            ))
        ));

        // حذف البيانات المحلية بغض النظر عن نتيجة الطلب
        $this->license_key = '';
        $this->license_status = 'inactive';
        $this->save_license_data();

        if (is_wp_error($response)) {
            return array('success' => true, 'message' => 'تم إلغاء تفعيل الترخيص محلياً (فشل الاتصال بالخادم)');
        }

        return array('success' => true, 'message' => 'تم إلغاء تفعيل الترخيص بنجاح');
    }



    /**
     * إضافة قائمة إدارة الترخيص (صفحة واحدة دائماً)
     */
    public function add_license_management_menu() {
        add_menu_page(
            $this->plugin_name . ' - إدارة الترخيص',
            $this->plugin_name,
            'manage_options',
            'ridcodtoken-license-' . $this->plugin_info['plugin_slug'],
            array($this, 'render_license_page'),
            'dashicons-lock',
            30
        );
    }

    /**
     * عرض صفحة إدارة الترخيص (صفحة واحدة للتفعيل والإدارة)
     */
    public function render_license_page() {
        $is_licensed = $this->is_license_valid();
        ?>
        <div class="wrap">
            <h1><?php echo esc_html($this->plugin_name); ?> - إدارة الترخيص</h1>

            <?php if (!$is_licensed): ?>
                <!-- قسم تفعيل الترخيص -->
                <div class="notice notice-warning">
                    <p><strong>تنبيه:</strong> هذه الإضافة تتطلب ترخيص صحيح للعمل. يرجى إدخال مفتاح الترخيص أدناه.</p>
                </div>

                <div class="card" style="max-width: 600px;">
                    <h2>تفعيل الترخيص</h2>
                    <form id="ridcodtoken-activation-form">
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="license_key">مفتاح الترخيص</label>
                                </th>
                                <td>
                                    <input type="text" id="license_key" name="license_key" class="regular-text" placeholder="أدخل مفتاح الترخيص هنا" required />
                                    <p class="description">أدخل مفتاح الترخيص الذي حصلت عليه عند الشراء</p>
                                </td>
                            </tr>
                        </table>

                        <p class="submit">
                            <button type="submit" class="button button-primary" id="activate-license-btn">
                                <span class="text">تفعيل الترخيص</span>
                                <span class="spinner" style="display: none;"></span>
                            </button>
                        </p>
                    </form>
                </div>

            <?php else: ?>
                <!-- قسم إدارة الترخيص -->
                <div class="notice notice-success">
                    <p><strong>ممتاز!</strong> الترخيص مفعل وصحيح.</p>
                </div>

                <div class="card" style="max-width: 600px;">
                    <h2>معلومات الترخيص</h2>
                    <table class="form-table">
                        <tr>
                            <th scope="row">مفتاح الترخيص</th>
                            <td>
                                <code><?php echo esc_html(substr($this->license_key, 0, 8) . '****' . substr($this->license_key, -8)); ?></code>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">حالة الترخيص</th>
                            <td><span style="color: #00a32a; font-weight: bold;">مفعل</span></td>
                        </tr>
                        <tr>
                            <th scope="row">آخر تحقق</th>
                            <td><?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $this->last_check)); ?></td>
                        </tr>
                        <tr>
                            <th scope="row">الموقع المسجل</th>
                            <td><?php echo esc_html(home_url()); ?></td>
                        </tr>
                    </table>

                    <p class="submit">
                        <button type="button" class="button button-secondary" id="check-license-btn">
                            <span class="text">فحص الترخيص الآن</span>
                            <span class="spinner" style="display: none;"></span>
                        </button>

                        <button type="button" class="button button-link-delete" id="deactivate-license-btn" style="margin-left: 10px;">
                            إلغاء تفعيل الترخيص
                        </button>
                    </p>
                </div>
            <?php endif; ?>

            <div id="license-message" style="margin-top: 20px;"></div>
        </div>
        <?php
    }



    /**
     * تحميل ملفات JavaScript و CSS
     */
    public function enqueue_admin_scripts($hook) {

        // تحميل الملفات فقط في صفحة الترخيص
        if (strpos($hook, 'ridcodtoken-license-' . $this->plugin_info['plugin_slug']) === false) {
            return;
        }

        wp_enqueue_script(
            'ridcodtoken-sdk-admin',
            plugin_dir_url(__FILE__) . 'assets/admin.js',
            array('jquery'),
            $this->plugin_info['version'],
            true
        );

        wp_localize_script('ridcodtoken-sdk-admin', 'ridcodtoken_sdk', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ridcodtoken_sdk_nonce'),
            'plugin_slug' => $this->plugin_info['plugin_slug'],
            'messages' => array(
                'activating' => 'جاري تفعيل الترخيص...',
                'checking' => 'جاري فحص الترخيص...',
                'deactivating' => 'جاري إلغاء التفعيل...',
                'success' => 'تم بنجاح!',
                'error' => 'حدث خطأ!',
                'confirm_deactivate' => 'هل أنت متأكد من إلغاء تفعيل الترخيص؟'
            )
        ));

        wp_enqueue_style(
            'ridcodtoken-sdk-admin',
            plugin_dir_url(__FILE__) . 'assets/admin.css',
            array(),
            $this->plugin_info['version']
        );
    }

    /**
     * معالج AJAX لتفعيل الترخيص
     */
    public function ajax_activate_license() {

        // التحقق من الأمان
        if (!wp_verify_nonce($_POST['nonce'], 'ridcodtoken_sdk_nonce')) {
            wp_send_json_error(array('message' => 'فشل في التحقق من الأمان'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'ليس لديك صلاحية لهذا الإجراء'));
        }

        $license_key = sanitize_text_field($_POST['license_key']);

        if (empty($license_key)) {
            wp_send_json_error(array('message' => 'يرجى إدخال مفتاح الترخيص'));
        }

        $result = $this->activate_license($license_key);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }

    /**
     * معالج AJAX لإلغاء تفعيل الترخيص
     */
    public function ajax_deactivate_license() {

        // التحقق من الأمان
        if (!wp_verify_nonce($_POST['nonce'], 'ridcodtoken_sdk_nonce')) {
            wp_send_json_error(array('message' => 'فشل في التحقق من الأمان'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'ليس لديك صلاحية لهذا الإجراء'));
        }

        $result = $this->deactivate_license();

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }

    /**
     * معالج AJAX لفحص حالة الترخيص
     */
    public function ajax_check_license_status() {

        // التحقق من الأمان
        if (!wp_verify_nonce($_POST['nonce'], 'ridcodtoken_sdk_nonce')) {
            wp_send_json_error(array('message' => 'فشل في التحقق من الأمان'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'ليس لديك صلاحية لهذا الإجراء'));
        }

        // فحص الترخيص مع الخادم
        $is_valid = $this->verify_license_with_server();

        if ($is_valid) {
            wp_send_json_success(array('message' => 'الترخيص صحيح ومفعل'));
        } else {
            wp_send_json_error(array('message' => 'الترخيص غير صحيح أو منتهي الصلاحية'));
        }
    }

    /**
     * إخفاء الإضافة من قائمة الإضافات عند عدم وجود ترخيص
     */
    public function hide_plugin_from_list($plugins) {

        if (!$this->is_license_valid()) {
            $plugin_basename = plugin_basename($this->plugin_file);
            if (isset($plugins[$plugin_basename])) {
                unset($plugins[$plugin_basename]);
            }
        }

        return $plugins;
    }

    /**
     * جدولة التحقق الدوري من الترخيص
     */
    public function schedule_license_check() {

        $hook_name = 'ridcodtoken_check_license_' . $this->plugin_info['plugin_slug'];

        if (!wp_next_scheduled($hook_name)) {
            wp_schedule_event(time(), 'ridcodtoken_5_hours', $hook_name);
        }
    }

    /**
     * فحص حالة الترخيص (يتم استدعاؤها بواسطة cron)
     */
    public function check_license_status() {

        if (!$this->verify_license_with_server()) {
            // إذا فشل التحقق، قم بإلغاء تفعيل الإضافة
            $this->license_status = 'inactive';
            $this->save_license_data();
        }
    }

    /**
     * إضافة فترة cron مخصصة (5 ساعات)
     */
    public static function add_cron_interval($schedules) {
        $schedules['ridcodtoken_5_hours'] = array(
            'interval' => 18000, // 5 * 60 * 60
            'display' => __('كل 5 ساعات')
        );
        return $schedules;
    }

    /**
     * التحقق من صحة الترخيص (للاستخدام الخارجي)
     */
    public function is_licensed() {
        return $this->is_license_valid();
    }

    /**
     * الحصول على معلومات الترخيص
     */
    public function get_license_info() {
        return array(
            'license_key' => $this->license_key,
            'status' => $this->license_status,
            'last_check' => $this->last_check,
            'is_valid' => $this->is_license_valid()
        );
    }
}

// إضافة فترة cron المخصصة
add_filter('cron_schedules', array('RIDCODTOKEN_Plugin_SDK', 'add_cron_interval'));

/**
 * دالة مساعدة لتهيئة SDK
 *
 * @param array $config إعدادات SDK
 * @return RIDCODTOKEN_Plugin_SDK
 */
function ridcodtoken_init_plugin_sdk($config) {
    return new RIDCODTOKEN_Plugin_SDK($config);
}
