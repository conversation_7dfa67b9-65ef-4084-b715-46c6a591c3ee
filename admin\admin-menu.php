<?php
/**
 * Admin Menu Setup for RIDCODTOKEN
 *
 * @package RIDCODTOKEN
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

// --- Menu Registration ---

/**
 * Adds the License Manager admin menu and submenus.
 */
function ridcodtoken_add_admin_menu() {
	// Main Menu Page: License Manager (List Table)
	add_menu_page(
		__( 'License Manager', 'ridcodtoken' ), // Page Title
		__( 'License Manager', 'ridcodtoken' ), // Menu Title
		'manage_options',                       // Capability required
		'ridcodtoken-license-manager',          // Menu Slug (main page)
		'ridcodtoken_render_license_manager_page', // Callback function
		'dashicons-admin-network',              // Icon URL
		30                                      // Position
	);

	// Submenu Page: License Manager (duplicates main page link)
    add_submenu_page(
        'ridcodtoken-license-manager',          // Parent Slug
        __( 'All Licenses', 'ridcodtoken' ),     // Page Title
        __( 'All Licenses', 'ridcodtoken' ),     // Menu Title
        'manage_options',                       // Capability
        'ridcodtoken-license-manager',          // Menu Slug (same as parent)
        'ridcodtoken_render_license_manager_page' // Callback function (same as parent)
    );
	// Submenu Page: Add New License
	add_submenu_page(
		'ridcodtoken-license-manager',          // Parent Slug
		__( 'Add New License', 'ridcodtoken' ),  // Page Title
		__( 'Add New', 'ridcodtoken' ),          // Menu Title
		'manage_options',                       // Capability
		'ridcodtoken-add-license',              // Menu Slug
		'ridcodtoken_render_add_license_page'   // Callback function
	);	// Submenu Page: License Plans Management
	add_submenu_page(
		'ridcodtoken-license-manager',          // Parent Slug
		__( 'License Plans', 'ridcodtoken' ),    // Page Title
		__( 'License Plans', 'ridcodtoken' ),    // Menu Title
		'manage_options',                       // Capability
		'ridcodtoken-license-plans',            // Menu Slug
		'ridcodtoken_render_license_plans_page' // Callback function
	);
	// Submenu Page: Email Settings
	add_submenu_page(
		'ridcodtoken-license-manager',          // Parent Slug
		__( 'Email Settings', 'ridcodtoken' ),  // Page Title
		__( 'Email Settings', 'ridcodtoken' ),  // Menu Title
		'manage_options',                       // Capability
		'ridcodtoken-email-settings',           // Menu Slug
		'ridcodtoken_render_email_settings_page' // Callback function
	);
}
add_action( 'admin_menu', 'ridcodtoken_add_admin_menu' );


// --- Page Rendering Callbacks ---

/**
 * Renders the License Manager (List Table) admin page content.
 */
function ridcodtoken_render_license_manager_page() {
	// Enqueue admin CSS
	wp_enqueue_style( 'ridcodtoken-admin-licenses', RIDCODTOKEN_PLUGIN_URL . 'assets/css/admin-licenses.css', array(), RIDCODTOKEN_VERSION );
	
	// Ensure List Table class is available
	if ( ! class_exists( 'RIDCODTOKEN_Licenses_List_Table' ) ) {
		require_once RIDCODTOKEN_PLUGIN_DIR . 'admin/class-ridcodtoken-licenses-list-table.php';
	}

	// Get statistics
	global $wpdb;
	$table_licenses = $wpdb->prefix . 'ridcod_licenses';
	$stats = array(
		'total' => $wpdb->get_var( "SELECT COUNT(*) FROM {$table_licenses}" ),
		'active' => $wpdb->get_var( "SELECT COUNT(*) FROM {$table_licenses} WHERE status = 'active'" ),
		'inactive' => $wpdb->get_var( "SELECT COUNT(*) FROM {$table_licenses} WHERE status = 'inactive'" ),
		'expired' => $wpdb->get_var( "SELECT COUNT(*) FROM {$table_licenses} WHERE expires_at IS NOT NULL AND expires_at < NOW()" )
	);

	// Removed the check for RIDCODTOKEN's own activation status, as it's not needed for the admin manager.
	$list_table = new RIDCODTOKEN_Licenses_List_Table();
	$list_table->prepare_items();
	?>
	<div class="wrap ridcodtoken-license-manager">
		<h1><?php esc_html_e( 'License Manager', 'ridcodtoken' ); ?>
			<a href="<?php echo esc_url( admin_url( 'admin.php?page=ridcodtoken-add-license' ) ); ?>" class="page-title-action"><?php esc_html_e( 'Add New', 'ridcodtoken' ); ?></a>
		</h1>
		<?php settings_errors('ridcodtoken_notices'); // Display notices ?>
		
		<!-- Statistics Cards -->
		<div class="ridcodtoken-stats-cards">
			<div class="stats-card stats-total">
				<div class="stats-number"><?php echo esc_html( $stats['total'] ); ?></div>
				<div class="stats-label"><?php esc_html_e( 'Total Licenses', 'ridcodtoken' ); ?></div>
			</div>
			<div class="stats-card stats-active">
				<div class="stats-number"><?php echo esc_html( $stats['active'] ); ?></div>
				<div class="stats-label"><?php esc_html_e( 'Active', 'ridcodtoken' ); ?></div>
			</div>
			<div class="stats-card stats-inactive">
				<div class="stats-number"><?php echo esc_html( $stats['inactive'] ); ?></div>
				<div class="stats-label"><?php esc_html_e( 'Inactive', 'ridcodtoken' ); ?></div>
			</div>
			<div class="stats-card stats-expired">
				<div class="stats-number"><?php echo esc_html( $stats['expired'] ); ?></div>
				<div class="stats-label"><?php esc_html_e( 'Expired', 'ridcodtoken' ); ?></div>
			</div>
		</div>
		
		<form method="post">
			<?php // For bulk actions ?>
			<input type="hidden" name="page" value="<?php echo esc_attr( $_REQUEST['page'] ); ?>" />
			<?php $list_table->search_box( __( 'Search Licenses', 'ridcodtoken' ), 'ridcodtoken-search-input' ); ?>
			<?php $list_table->display(); ?>
		</form>
	</div>
	<?php
}

/**
 * Renders the Add New License admin page content AND handles form submission.
 */
function ridcodtoken_render_add_license_page() {

    // --- Process Form Submission ---
	if ( isset( $_POST['ridcodtoken_add_license_nonce'] ) && wp_verify_nonce( sanitize_key( $_POST['ridcodtoken_add_license_nonce'] ), 'ridcodtoken_add_new_license_action' ) ) {

		if ( ! current_user_can( 'manage_options' ) ) {
			wp_die( __( 'You do not have sufficient permissions to add licenses.', 'ridcodtoken' ) );
		}

		global $wpdb;
		$table_licenses = $wpdb->prefix . 'ridcod_licenses';
		$errors = array();

		// Sanitize and validate input
		$user_email = isset( $_POST['user_email'] ) ? sanitize_email( $_POST['user_email'] ) : '';
		if ( ! empty( $user_email ) && ! is_email( $user_email ) ) {
			$errors[] = __( 'Invalid user email format.', 'ridcodtoken' );
			$user_email = '';
		}

		$sites_allowed = isset( $_POST['sites_allowed'] ) ? absint( $_POST['sites_allowed'] ) : 1;
		if ( $sites_allowed <= 0 ) $sites_allowed = 1;

		$status = isset( $_POST['status'] ) && in_array( $_POST['status'], ['active', 'inactive'] ) ? sanitize_key( $_POST['status'] ) : 'inactive';

		$expires_at_input = isset( $_POST['expires_at'] ) ? sanitize_text_field( $_POST['expires_at'] ) : '';
		$expires_at = null;
		if ( ! empty( $expires_at_input ) ) {
			$timestamp = strtotime( $expires_at_input );
			if ( $timestamp ) {
				$expires_at = date( 'Y-m-d H:i:s', $timestamp );
			} else {
				$errors[] = __( 'Invalid expiration date format. Please use YYYY-MM-DD.', 'ridcodtoken' );
			}
		}

		$license_key = isset( $_POST['license_key'] ) ? trim( sanitize_text_field( $_POST['license_key'] ) ) : '';

		$product_id = isset( $_POST['product_id'] ) ? absint( $_POST['product_id'] ) : 0;
		if ( $product_id <= 0 ) {
			$errors[] = __( 'Product ID is required and must be a valid number.', 'ridcodtoken' );
		}

		// Generate key if empty
		if ( empty( $license_key ) ) {
			if ( function_exists('ridcodtoken_create_unique_license_key') ) {
                $license_key = ridcodtoken_create_unique_license_key();
                if ( ! $license_key ) {
                    $errors[] = __( 'Failed to automatically generate a unique license key. Please try entering one manually or try again.', 'ridcodtoken' );
                }
            } else {
                 $errors[] = __( 'Error: Key generation function is missing.', 'ridcodtoken' );
            }
		} else {
			// Check if manually entered key already exists
			$existing = $wpdb->get_var( $wpdb->prepare( "SELECT id FROM $table_licenses WHERE license_key = %s", $license_key ) );
			if ( $existing ) {
				$errors[] = __( 'The provided license key already exists. Please enter a unique key or leave it blank to auto-generate.', 'ridcodtoken' );
			}
		}

		// If no errors, insert into database
		if ( empty( $errors ) ) {
			$inserted = $wpdb->insert(
				$table_licenses,
				array(
					'license_key'   => $license_key,
					'user_email'    => $user_email,
					'status'        => $status,
					'sites_allowed' => $sites_allowed,
					'order_id'      => null,
					'product_id'    => $product_id,
					'created_at'    => current_time( 'mysql', 1 ),
					'expires_at'    => $expires_at,
				),
				array( '%s', '%s', '%s', '%d', '%d', '%d', '%s', '%s' )
			);

			if ( $inserted ) {
				add_settings_error( 'ridcodtoken_notices', 'license_added', __( 'License added successfully.', 'ridcodtoken' ), 'updated' );
				// Redirect after successful submission
                // Use wp_redirect which handles exiting script execution
                wp_redirect( admin_url( 'admin.php?page=ridcodtoken-license-manager' ) );
				exit;
			} else {
				$errors[] = __( 'Failed to insert license into the database.', 'ridcodtoken' );
			}
		}

		// If errors occurred, add them as settings errors to display on the form page
		if ( ! empty( $errors ) ) {
			 foreach ($errors as $error) {
				 add_settings_error('ridcodtoken_notices', 'add_license_error', $error, 'error');
			 }
		}
	}
    // --- End Form Processing ---


	// Enqueue datepicker scripts/styles
	wp_enqueue_script('jquery-ui-datepicker');
	wp_enqueue_style('jquery-ui-style', '//ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css');

	?>
	<div class="wrap">
		<h1><?php esc_html_e( 'Add New License', 'ridcodtoken' ); ?></h1>
		<?php settings_errors('ridcodtoken_notices'); // Display notices ?>

		<form method="post" action="<?php echo esc_url( admin_url( 'admin.php?page=ridcodtoken-add-license' ) ); ?>">
			<?php wp_nonce_field( 'ridcodtoken_add_new_license_action', 'ridcodtoken_add_license_nonce' ); ?>

			<table class="form-table" role="presentation">
				<tbody>
					<tr>
						<th scope="row"><label for="license_key"><?php esc_html_e( 'License Key', 'ridcodtoken' ); ?></label></th>
						<td>
							<input name="license_key" type="text" id="license_key" value="" class="regular-text" />
							<p class="description"><?php esc_html_e( 'Leave blank to auto-generate a unique key.', 'ridcodtoken' ); ?></p>
						</td>
					</tr>
					<tr>
						<th scope="row"><label for="user_email"><?php esc_html_e( 'User Email', 'ridcodtoken' ); ?></label></th>
						<td><input name="user_email" type="email" id="user_email" value="" class="regular-text" /></td>
					</tr>
					 <tr>
						<th scope="row"><label for="status"><?php esc_html_e( 'Status', 'ridcodtoken' ); ?></label></th>
						<td>
							<select name="status" id="status">
								<option value="active"><?php esc_html_e( 'Active', 'ridcodtoken' ); ?></option>
								<option value="inactive" selected><?php esc_html_e( 'Inactive', 'ridcodtoken' ); ?></option>
							</select>
						</td>
					</tr>
					<tr>
						<th scope="row"><label for="sites_allowed"><?php esc_html_e( 'Sites Allowed', 'ridcodtoken' ); ?></label></th>
						<td><input name="sites_allowed" type="number" step="1" min="1" id="sites_allowed" value="1" class="small-text" /></td>
					</tr>
					<tr>
						<th scope="row"><label for="product_id"><?php esc_html_e( 'Product ID', 'ridcodtoken' ); ?> <span style="color: red;">*</span></label></th>
						<td>
							<input name="product_id" type="number" step="1" min="1" id="product_id" value="" class="regular-text" required />
							<p class="description"><?php esc_html_e( 'WooCommerce Product ID that this license is for (required).', 'ridcodtoken' ); ?></p>
						</td>
					</tr>
					<tr>
						<th scope="row"><label for="expires_at"><?php esc_html_e( 'Expires At', 'ridcodtoken' ); ?></label></th>
						<td>
							<input name="expires_at" type="text" id="expires_at" value="" class="ridcodtoken-datepicker regular-text" placeholder="YYYY-MM-DD" autocomplete="off" />
							<p class="description"><?php esc_html_e( 'Leave blank for a lifetime license.', 'ridcodtoken' ); ?></p>
						</td>
					</tr>
				</tbody>
			</table>
			<?php submit_button( __( 'Add License', 'ridcodtoken' ) ); ?>
		</form>
	</div>
	<script type="text/javascript">
		jQuery(document).ready(function($){
			$('.ridcodtoken-datepicker').datepicker({
				dateFormat: 'yy-mm-dd',
                changeMonth: true,
                changeYear: true,
			});
		});
	</script>
	<?php
}








// --- Action Handlers & Admin Notices ---

/**
 * Handles actions from the License List Table (delete, activate, deactivate).
 */
function ridcodtoken_handle_license_actions() {
	// Ensure List Table class is available if needed for current_action()
	if ( ! class_exists( 'RIDCODTOKEN_Licenses_List_Table' ) ) {
		require_once RIDCODTOKEN_PLUGIN_DIR . 'admin/class-ridcodtoken-licenses-list-table.php';
	}
	$list_table = new RIDCODTOKEN_Licenses_List_Table();
	$action = $list_table->current_action();

	if ( $action ) {
		$nonce = isset($_REQUEST['_wpnonce']) ? sanitize_key($_REQUEST['_wpnonce']) : '';
		$license_id = isset($_REQUEST['license']) ? absint($_REQUEST['license']) : 0;
		$nonce_action_base = 'ridcodtoken_';

		if ($license_id && ($action === 'delete' || $action === 'activate' || $action === 'deactivate')) {
			 if (!wp_verify_nonce($nonce, $nonce_action_base . $action . '_' . $license_id)) {
				wp_die(__('Security check failed!', 'ridcodtoken'));
			}

			global $wpdb;
			$table_licenses = $wpdb->prefix . 'ridcod_licenses';
			$table_sites = $wpdb->prefix . 'ridcod_license_sites';

			if ( 'delete' === $action ) {
				$wpdb->delete( $table_licenses, array( 'id' => $license_id ), array( '%d' ) );
				$wpdb->delete( $table_sites, array( 'license_id' => $license_id ), array( '%d' ) );
				add_settings_error('ridcodtoken_notices', 'license_deleted', __('License deleted successfully.', 'ridcodtoken'), 'updated');
			} elseif ( 'activate' === $action ) {
				$wpdb->update( $table_licenses, array( 'status' => 'active' ), array( 'id' => $license_id ), array( '%s' ), array( '%d' ) );
				 add_settings_error('ridcodtoken_notices', 'license_activated', __('License activated successfully.', 'ridcodtoken'), 'updated');
			} elseif ( 'deactivate' === $action ) {
				 $wpdb->update( $table_licenses, array( 'status' => 'inactive' ), array( 'id' => $license_id ), array( '%s' ), array( '%d' ) );
				 add_settings_error('ridcodtoken_notices', 'license_deactivated', __('License deactivated successfully.', 'ridcodtoken'), 'updated');
			}
            // Add a flag to prevent running the add license handler after this one potentially redirects
            $_POST['_ridcodtoken_action_handled'] = true;
		}
		// Handle Bulk Actions here if implemented
	}
}
// Hook the handler to the load action for the main license manager page
add_action( 'load-toplevel_page_ridcodtoken-license-manager', 'ridcodtoken_handle_license_actions' );


// Add this before the admin notice function at the end
/**
 * Renders the License Plans management page content.
 */
function ridcodtoken_render_license_plans_page() {
	// تحميل ملف CSS للتوسيع
	wp_enqueue_style('ridcodtoken-admin-plans', RIDCODTOKEN_PLUGIN_URL . 'assets/css/admin-plans.css', array(), RIDCODTOKEN_VERSION);
	
	global $wpdb;
	$table_plans = $wpdb->prefix . 'ridcod_license_plans';
	
	// Handle form submissions
	if (isset($_POST['action'])) {
		if (!wp_verify_nonce($_POST['ridcodtoken_plans_nonce'], 'ridcodtoken_plans_action')) {
			wp_die(__('Security check failed.', 'ridcodtoken'));
		}

		if (!current_user_can('manage_options')) {
			wp_die(__('You do not have sufficient permissions.', 'ridcodtoken'));
		}

		$action = sanitize_key($_POST['action']);
		
		if ($action === 'add_plan') {
			// Add new plan
			$plan_name = sanitize_text_field($_POST['plan_name']);
			$product_id = absint($_POST['product_id']);
			$sites_allowed = absint($_POST['sites_allowed']);
			$duration_days = isset($_POST['duration_days']) && $_POST['duration_days'] !== '' ? absint($_POST['duration_days']) : null;
			
			if (empty($plan_name) || $product_id <= 0 || $sites_allowed <= 0) {
				add_settings_error('ridcodtoken_notices', 'plan_error', __('Please fill all required fields correctly.', 'ridcodtoken'), 'error');
			} else {
				// Check if product ID already exists
				$existing = $wpdb->get_var($wpdb->prepare("SELECT id FROM $table_plans WHERE product_id = %d", $product_id));
				if ($existing) {
					add_settings_error('ridcodtoken_notices', 'plan_error', __('Product ID already exists in another plan.', 'ridcodtoken'), 'error');
				} else {
					$inserted = $wpdb->insert(
						$table_plans,
						array(
							'plan_name' => $plan_name,
							'product_id' => $product_id,
							'sites_allowed' => $sites_allowed,
							'duration_days' => $duration_days,
							'status' => 'active'
						),
						array('%s', '%d', '%d', '%d', '%s')
					);
					
					if ($inserted) {
						add_settings_error('ridcodtoken_notices', 'plan_success', __('Plan added successfully.', 'ridcodtoken'), 'updated');
					} else {
						add_settings_error('ridcodtoken_notices', 'plan_error', __('Failed to add plan.', 'ridcodtoken'), 'error');
					}
				}
			}
		} elseif ($action === 'edit_plan') {
			// Edit existing plan
			$plan_id = absint($_POST['plan_id']);
			$plan_name = sanitize_text_field($_POST['plan_name']);
			$product_id = absint($_POST['product_id']);
			$sites_allowed = absint($_POST['sites_allowed']);
			$duration_days = isset($_POST['duration_days']) && $_POST['duration_days'] !== '' ? absint($_POST['duration_days']) : null;
			$status = sanitize_key($_POST['status']);
			
			if (empty($plan_name) || $product_id <= 0 || $sites_allowed <= 0 || $plan_id <= 0) {
				add_settings_error('ridcodtoken_notices', 'plan_error', __('Please fill all required fields correctly.', 'ridcodtoken'), 'error');
			} else {
				// Check if product ID already exists in another plan
				$existing = $wpdb->get_var($wpdb->prepare("SELECT id FROM $table_plans WHERE product_id = %d AND id != %d", $product_id, $plan_id));
				if ($existing) {
					add_settings_error('ridcodtoken_notices', 'plan_error', __('Product ID already exists in another plan.', 'ridcodtoken'), 'error');
				} else {
					$updated = $wpdb->update(
						$table_plans,
						array(
							'plan_name' => $plan_name,
							'product_id' => $product_id,
							'sites_allowed' => $sites_allowed,
							'duration_days' => $duration_days,
							'status' => $status
						),
						array('id' => $plan_id),
						array('%s', '%d', '%d', '%d', '%s'),
						array('%d')
					);
					
					if ($updated !== false) {
						add_settings_error('ridcodtoken_notices', 'plan_success', __('Plan updated successfully.', 'ridcodtoken'), 'updated');
					} else {
						add_settings_error('ridcodtoken_notices', 'plan_error', __('Failed to update plan.', 'ridcodtoken'), 'error');
					}
				}
			}
		} elseif ($action === 'delete_plan') {
			// Delete plan
			$plan_id = absint($_POST['plan_id']);
			if ($plan_id > 0) {
				$deleted = $wpdb->delete($table_plans, array('id' => $plan_id), array('%d'));
				if ($deleted) {
					add_settings_error('ridcodtoken_notices', 'plan_success', __('Plan deleted successfully.', 'ridcodtoken'), 'updated');
				} else {
					add_settings_error('ridcodtoken_notices', 'plan_error', __('Failed to delete plan.', 'ridcodtoken'), 'error');
				}
			}
		}
	}
	
	// Get current plans
	$plans = $wpdb->get_results("SELECT * FROM $table_plans ORDER BY created_at DESC");
	
	// Get edit plan data if editing
	$edit_plan = null;
	if (isset($_GET['edit']) && absint($_GET['edit']) > 0) {
		$edit_plan = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_plans WHERE id = %d", absint($_GET['edit'])));
	}	?>
	
	<div class="wrap ridcodtoken-plans-page">
		<h1><?php esc_html_e('License Plans Management', 'ridcodtoken'); ?></h1>
		<?php settings_errors('ridcodtoken_notices'); ?>
		
		<!-- Add/Edit Plan Form -->
		<div class="card" style="max-width: 95%; margin-bottom: 20px;">
			<h2><?php echo $edit_plan ? esc_html__('Edit Plan', 'ridcodtoken') : esc_html__('Add New Plan', 'ridcodtoken'); ?></h2>
			<form method="post" action="">
				<?php wp_nonce_field('ridcodtoken_plans_action', 'ridcodtoken_plans_nonce'); ?>
				<input type="hidden" name="action" value="<?php echo $edit_plan ? 'edit_plan' : 'add_plan'; ?>">
				<?php if ($edit_plan): ?>
					<input type="hidden" name="plan_id" value="<?php echo esc_attr($edit_plan->id); ?>">
				<?php endif; ?>
				
				<table class="form-table">
					<tr>
						<th scope="row"><label for="plan_name"><?php esc_html_e('Plan Name', 'ridcodtoken'); ?></label></th>
						<td><input name="plan_name" type="text" id="plan_name" value="<?php echo $edit_plan ? esc_attr($edit_plan->plan_name) : ''; ?>" class="regular-text" required /></td>
					</tr>
					<tr>
						<th scope="row"><label for="product_id"><?php esc_html_e('WooCommerce Product ID', 'ridcodtoken'); ?></label></th>
						<td><input name="product_id" type="number" id="product_id" value="<?php echo $edit_plan ? esc_attr($edit_plan->product_id) : ''; ?>" class="small-text" min="1" required /></td>
					</tr>
					<tr>
						<th scope="row"><label for="sites_allowed"><?php esc_html_e('Sites Allowed', 'ridcodtoken'); ?></label></th>
						<td><input name="sites_allowed" type="number" id="sites_allowed" value="<?php echo $edit_plan ? esc_attr($edit_plan->sites_allowed) : '1'; ?>" class="small-text" min="1" required /></td>
					</tr>
					<tr>
						<th scope="row"><label for="duration_days"><?php esc_html_e('Duration (Days)', 'ridcodtoken'); ?></label></th>
						<td>
							<input name="duration_days" type="number" id="duration_days" value="<?php echo $edit_plan ? esc_attr($edit_plan->duration_days) : ''; ?>" class="small-text" min="1" />
							<p class="description"><?php esc_html_e('Leave empty for lifetime license.', 'ridcodtoken'); ?></p>
						</td>
					</tr>
					<?php if ($edit_plan): ?>
					<tr>
						<th scope="row"><label for="status"><?php esc_html_e('Status', 'ridcodtoken'); ?></label></th>
						<td>
							<select name="status" id="status">
								<option value="active" <?php selected($edit_plan->status, 'active'); ?>><?php esc_html_e('Active', 'ridcodtoken'); ?></option>
								<option value="inactive" <?php selected($edit_plan->status, 'inactive'); ?>><?php esc_html_e('Inactive', 'ridcodtoken'); ?></option>
							</select>
						</td>
					</tr>
					<?php endif; ?>
				</table>
				
				<?php submit_button($edit_plan ? __('Update Plan', 'ridcodtoken') : __('Add Plan', 'ridcodtoken')); ?>
				
				<?php if ($edit_plan): ?>
					<a href="<?php echo esc_url(admin_url('admin.php?page=ridcodtoken-license-plans')); ?>" class="button"><?php esc_html_e('Cancel', 'ridcodtoken'); ?></a>
				<?php endif; ?>
			</form>
		</div>
				<!-- Plans List -->
		<div class="card" style="max-width: 95%;">
			<h2><?php esc_html_e('Current Plans', 'ridcodtoken'); ?></h2>
			<?php if (empty($plans)): ?>
				<p><?php esc_html_e('No plans found. Add your first plan above.', 'ridcodtoken'); ?></p>
			<?php else: ?>
				<table class="wp-list-table widefat fixed striped">
					<thead>
						<tr>
							<th><?php esc_html_e('Plan Name', 'ridcodtoken'); ?></th>
							<th><?php esc_html_e('Product ID', 'ridcodtoken'); ?></th>
							<th><?php esc_html_e('Sites Allowed', 'ridcodtoken'); ?></th>
							<th><?php esc_html_e('Duration', 'ridcodtoken'); ?></th>
							<th><?php esc_html_e('Status', 'ridcodtoken'); ?></th>
							<th><?php esc_html_e('Created', 'ridcodtoken'); ?></th>
							<th><?php esc_html_e('Actions', 'ridcodtoken'); ?></th>
						</tr>
					</thead>
					<tbody>
						<?php foreach ($plans as $plan): ?>
							<tr>
								<td><strong><?php echo esc_html($plan->plan_name); ?></strong></td>
								<td><?php echo esc_html($plan->product_id); ?></td>
								<td><?php echo esc_html($plan->sites_allowed); ?></td>
								<td><?php echo $plan->duration_days ? esc_html($plan->duration_days . ' ' . __('days', 'ridcodtoken')) : esc_html__('Lifetime', 'ridcodtoken'); ?></td>
								<td>
									<span class="<?php echo $plan->status === 'active' ? 'ridcodtoken-status-active' : 'ridcodtoken-status-inactive'; ?>">
										<?php echo $plan->status === 'active' ? esc_html__('Active', 'ridcodtoken') : esc_html__('Inactive', 'ridcodtoken'); ?>
									</span>
								</td>
								<td><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($plan->created_at))); ?></td>
								<td>
									<a href="<?php echo esc_url(admin_url('admin.php?page=ridcodtoken-license-plans&edit=' . $plan->id)); ?>" class="button button-small"><?php esc_html_e('Edit', 'ridcodtoken'); ?></a>
									<form method="post" style="display: inline;" onsubmit="return confirm('<?php esc_attr_e('Are you sure you want to delete this plan?', 'ridcodtoken'); ?>');">
										<?php wp_nonce_field('ridcodtoken_plans_action', 'ridcodtoken_plans_nonce'); ?>
										<input type="hidden" name="action" value="delete_plan">
										<input type="hidden" name="plan_id" value="<?php echo esc_attr($plan->id); ?>">
										<button type="submit" class="button button-small button-link-delete"><?php esc_html_e('Delete', 'ridcodtoken'); ?></button>
									</form>
								</td>
							</tr>
						<?php endforeach; ?>
					</tbody>
				</table>
			<?php endif; ?>
		</div>
		
		<style>
		.ridcodtoken-status-active { color: #46b450; font-weight: bold; }
		.ridcodtoken-status-inactive { color: #dc3232; font-weight: bold; }
		</style>
	</div>
	<?php
}

/**
 * Renders the Email Settings management page content.
 */
function ridcodtoken_render_email_settings_page() {
	// تحميل ملف CSS وJS للتوسيط
	wp_enqueue_style('ridcodtoken-admin-email-settings', RIDCODTOKEN_PLUGIN_URL . 'assets/css/admin-email-settings.css', array(), RIDCODTOKEN_VERSION);
	wp_enqueue_script('ridcodtoken-email-settings', RIDCODTOKEN_PLUGIN_URL . 'assets/js/ridcodtoken-email-settings.js', array('jquery'), RIDCODTOKEN_VERSION, true);
	
	// معالجة إرسال رسالة تجريبية
	if (isset($_POST['ridcodtoken_test_email_nonce']) && wp_verify_nonce($_POST['ridcodtoken_test_email_nonce'], 'ridcodtoken_test_email_action')) {
		if (!current_user_can('manage_options')) {
			wp_die(__('You do not have sufficient permissions.', 'ridcodtoken'));
		}

		$test_email = sanitize_email($_POST['test_email']);
		$email_type = sanitize_text_field($_POST['email_type']);
		
		if (!is_email($test_email)) {
			add_settings_error('ridcodtoken_email_notices', 'invalid_email', __('Please enter a valid email address.', 'ridcodtoken'), 'error');
		} else {
			// إرسال رسالة تجريبية
			if ($email_type === 'purchase') {
				ridcodtoken_send_test_purchase_email($test_email);
			} elseif ($email_type === 'expiry') {
				ridcodtoken_send_test_expiry_email($test_email);
			}
			add_settings_error('ridcodtoken_email_notices', 'test_email_sent', 
				sprintf(__('Test email sent successfully to %s', 'ridcodtoken'), $test_email), 'updated');
		}
	}

	// معالجة إرسال النموذج
	if (isset($_POST['ridcodtoken_email_settings_nonce']) && wp_verify_nonce($_POST['ridcodtoken_email_settings_nonce'], 'ridcodtoken_email_settings_action')) {
		if (!current_user_can('manage_options')) {
			wp_die(__('You do not have sufficient permissions.', 'ridcodtoken'));
		}

		// حفظ إعدادات الرسائل
		$email_settings = array(
			'purchase_email_enabled' => isset($_POST['purchase_email_enabled']) ? 1 : 0,
			'purchase_email_subject' => sanitize_text_field($_POST['purchase_email_subject']),
			'purchase_email_content' => wp_kses_post($_POST['purchase_email_content']),
			
			'expiry_email_enabled' => isset($_POST['expiry_email_enabled']) ? 1 : 0,
			'expiry_email_subject' => sanitize_text_field($_POST['expiry_email_subject']),
			'expiry_email_content' => wp_kses_post($_POST['expiry_email_content']),
			'expiry_email_days' => absint($_POST['expiry_email_days']),
		);
		
		update_option('ridcodtoken_email_settings', $email_settings);
		add_settings_error('ridcodtoken_email_notices', 'settings_saved', __('Email settings saved successfully.', 'ridcodtoken'), 'updated');
	}
	
	// جلب الإعدادات الحالية
	$email_settings = get_option('ridcodtoken_email_settings', array(
		'purchase_email_enabled' => 1,
		'purchase_email_subject' => __('Your License Key(s) for Order #{order_number}', 'ridcodtoken'),
		'purchase_email_content' => __('<p>Thank you for your purchase!</p><p>Here are your license key(s):</p>{license_keys}<p>Please keep these keys safe.</p>', 'ridcodtoken'),
		
		'expiry_email_enabled' => 1,
		'expiry_email_subject' => __('Your License Key is Expiring Soon', 'ridcodtoken'),
		'expiry_email_content' => __('<p>Hello,</p><p>This is a friendly reminder that your license key <strong>{license_key}</strong> is set to expire on {expiry_date}.</p><p>Please renew your license to continue receiving updates and support.</p><p>Thank you,<br>{site_name}</p>', 'ridcodtoken'),
		'expiry_email_days' => 7,
	));
	?>
	<div class="wrap ridcodtoken-email-settings">
		<h1><?php esc_html_e('Email Settings', 'ridcodtoken'); ?></h1>
		<?php settings_errors('ridcodtoken_email_notices'); ?>

		<form method="post" action="<?php echo esc_url(admin_url('admin.php?page=ridcodtoken-email-settings')); ?>">
			<?php wp_nonce_field('ridcodtoken_email_settings_action', 'ridcodtoken_email_settings_nonce'); ?>

			<!-- إعدادات رسالة الشراء -->
			<div class="email-setting-section">
				<h2><?php esc_html_e('Purchase Email Settings', 'ridcodtoken'); ?></h2>
				<p class="description"><?php esc_html_e('Email sent to customers when they purchase a license.', 'ridcodtoken'); ?></p>
				
				<table class="form-table" role="presentation">
					<tbody>						<tr>
							<th scope="row"><?php esc_html_e('Enable Purchase Email', 'ridcodtoken'); ?></th>
							<td>
								<label>
									<input type="checkbox" name="purchase_email_enabled" value="1" <?php checked(isset($email_settings['purchase_email_enabled']) ? $email_settings['purchase_email_enabled'] : 0, 1); ?> />
									<?php esc_html_e('Send email when license is purchased', 'ridcodtoken'); ?>
								</label>
							</td>
						</tr>
						<tr>
							<th scope="row"><label for="purchase_email_subject"><?php esc_html_e('Email Subject', 'ridcodtoken'); ?></label></th>
							<td>
								<input name="purchase_email_subject" type="text" id="purchase_email_subject" value="<?php echo esc_attr(isset($email_settings['purchase_email_subject']) ? $email_settings['purchase_email_subject'] : ''); ?>" class="large-text" />
								<p class="description"><?php esc_html_e('Available placeholders: {order_number}, {customer_name}, {site_name}', 'ridcodtoken'); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><label for="purchase_email_content"><?php esc_html_e('Email Content', 'ridcodtoken'); ?></label></th>							<td>
								<?php 
								wp_editor(isset($email_settings['purchase_email_content']) ? $email_settings['purchase_email_content'] : '', 'purchase_email_content', array(
									'textarea_name' => 'purchase_email_content',
									'textarea_rows' => 10,
									'media_buttons' => false,
									'teeny' => true,
								)); 
								?>								<p class="description">
									<?php esc_html_e('Available placeholders:', 'ridcodtoken'); ?>
									<code>{license_keys}</code>, <code>{order_number}</code>, <code>{customer_name}</code>, <code>{site_name}</code>
								</p>
							</td>
						</tr>
					</tbody>
				</table>
			</div>

			<!-- إعدادات رسالة انتهاء الصلاحية -->
			<div class="email-setting-section">
				<h2><?php esc_html_e('Expiry Reminder Email Settings', 'ridcodtoken'); ?></h2>
				<p class="description"><?php esc_html_e('Email sent to customers when their license is about to expire.', 'ridcodtoken'); ?></p>
				
				<table class="form-table" role="presentation">
					<tbody>						<tr>
							<th scope="row"><?php esc_html_e('Enable Expiry Email', 'ridcodtoken'); ?></th>
							<td>
								<label>
									<input type="checkbox" name="expiry_email_enabled" value="1" <?php checked(isset($email_settings['expiry_email_enabled']) ? $email_settings['expiry_email_enabled'] : 0, 1); ?> />
									<?php esc_html_e('Send expiry reminder emails', 'ridcodtoken'); ?>
								</label>
							</td>
						</tr>
						<tr>
							<th scope="row"><label for="expiry_email_days"><?php esc_html_e('Days Before Expiry', 'ridcodtoken'); ?></label></th>
							<td>
								<input name="expiry_email_days" type="number" id="expiry_email_days" value="<?php echo esc_attr(isset($email_settings['expiry_email_days']) ? $email_settings['expiry_email_days'] : 7); ?>" class="small-text" min="1" max="30" />
								<p class="description"><?php esc_html_e('Number of days before expiry to send the reminder email.', 'ridcodtoken'); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><label for="expiry_email_subject"><?php esc_html_e('Email Subject', 'ridcodtoken'); ?></label></th>
							<td>
								<input name="expiry_email_subject" type="text" id="expiry_email_subject" value="<?php echo esc_attr(isset($email_settings['expiry_email_subject']) ? $email_settings['expiry_email_subject'] : ''); ?>" class="large-text" />
								<p class="description"><?php esc_html_e('Available placeholders: {license_key}, {expiry_date}, {site_name}', 'ridcodtoken'); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><label for="expiry_email_content"><?php esc_html_e('Email Content', 'ridcodtoken'); ?></label></th>							<td>
								<?php 
								wp_editor(isset($email_settings['expiry_email_content']) ? $email_settings['expiry_email_content'] : '', 'expiry_email_content', array(
									'textarea_name' => 'expiry_email_content',
									'textarea_rows' => 10,
									'media_buttons' => false,
									'teeny' => true,
								)); 
								?>
								<p class="description">
									<?php esc_html_e('Available placeholders:', 'ridcodtoken'); ?>
									<code>{license_key}</code>, <code>{expiry_date}</code>, <code>{customer_name}</code>, <code>{site_name}</code>
								</p>
							</td>
						</tr>
					</tbody>
				</table>
			</div>			<?php submit_button(__('Save Email Settings', 'ridcodtoken')); ?>
		</form>

		<!-- قسم إرسال رسالة تجريبية -->
		<div class="email-setting-section">
			<h2><?php esc_html_e('Send Test Email', 'ridcodtoken'); ?></h2>
			<p class="description"><?php esc_html_e('Send a test email to verify your email settings are working correctly.', 'ridcodtoken'); ?></p>
			
			<form method="post" action="<?php echo esc_url(admin_url('admin.php?page=ridcodtoken-email-settings')); ?>">
				<?php wp_nonce_field('ridcodtoken_test_email_action', 'ridcodtoken_test_email_nonce'); ?>
				
				<table class="form-table" role="presentation">
					<tbody>
						<tr>
							<th scope="row"><label for="test_email"><?php esc_html_e('Test Email Address', 'ridcodtoken'); ?></label></th>
							<td>
								<input name="test_email" type="email" id="test_email" value="<?php echo esc_attr(get_option('admin_email')); ?>" class="regular-text" required />
								<p class="description"><?php esc_html_e('Enter the email address where you want to send the test email.', 'ridcodtoken'); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><label for="email_type"><?php esc_html_e('Email Type', 'ridcodtoken'); ?></label></th>
							<td>
								<select name="email_type" id="email_type">
									<option value="purchase"><?php esc_html_e('Purchase Email', 'ridcodtoken'); ?></option>
									<option value="expiry"><?php esc_html_e('Expiry Email', 'ridcodtoken'); ?></option>
								</select>
								<p class="description"><?php esc_html_e('Choose which type of email to send as a test.', 'ridcodtoken'); ?></p>
							</td>
						</tr>
					</tbody>
				</table>
				
				<?php submit_button(__('Send Test Email', 'ridcodtoken'), 'secondary', 'send_test_email'); ?>
			</form>
		</div>

		<!-- معاينة الرسائل -->
		<div class="email-preview-section">
			<h2><?php esc_html_e('Email Preview', 'ridcodtoken'); ?></h2>
			<div class="email-preview-tabs">
				<button type="button" class="button preview-tab active" data-tab="purchase"><?php esc_html_e('Purchase Email', 'ridcodtoken'); ?></button>
				<button type="button" class="button preview-tab" data-tab="expiry"><?php esc_html_e('Expiry Email', 'ridcodtoken'); ?></button>
			</div>
					<div id="purchase-preview" class="email-preview-content active">
				<h3><?php esc_html_e('Purchase Email Preview', 'ridcodtoken'); ?></h3>
				<div class="email-preview-box">
					<strong><?php esc_html_e('Subject:', 'ridcodtoken'); ?></strong> <span class="preview-subject"><?php echo esc_html(isset($email_settings['purchase_email_subject']) ? $email_settings['purchase_email_subject'] : ''); ?></span>
					<hr>
					<div class="preview-content"><?php echo wp_kses_post(isset($email_settings['purchase_email_content']) ? $email_settings['purchase_email_content'] : ''); ?></div>
				</div>
			</div>
			
			<div id="expiry-preview" class="email-preview-content">
				<h3><?php esc_html_e('Expiry Email Preview', 'ridcodtoken'); ?></h3>
				<div class="email-preview-box">
					<strong><?php esc_html_e('Subject:', 'ridcodtoken'); ?></strong> <span class="preview-subject"><?php echo esc_html(isset($email_settings['expiry_email_subject']) ? $email_settings['expiry_email_subject'] : ''); ?></span>
					<hr>
					<div class="preview-content"><?php echo wp_kses_post(isset($email_settings['expiry_email_content']) ? $email_settings['expiry_email_content'] : ''); ?></div>
				</div>
			</div>
		</div>
	</div>

	<script>
	jQuery(document).ready(function($) {
		// التبديل بين معاينات الرسائل
		$('.preview-tab').on('click', function() {
			var tab = $(this).data('tab');
			
			$('.preview-tab').removeClass('active');
			$(this).addClass('active');
			
			$('.email-preview-content').removeClass('active');
			$('#' + tab + '-preview').addClass('active');
		});
		
		// تحديث المعاينة عند تغيير المحتوى
		$('#purchase_email_subject').on('input', function() {
			$('#purchase-preview .preview-subject').text($(this).val());
		});
		
		$('#expiry_email_subject').on('input', function() {
			$('#expiry-preview .preview-subject').text($(this).val());
		});
	});
	</script>
	<?php
}

/**
 * Sends a test purchase email
 */
function ridcodtoken_send_test_purchase_email($to_email) {
	$email_settings = get_option('ridcodtoken_email_settings', array());
	
	// Set default values if not set
	$email_settings = array_merge(array(
		'purchase_email_subject' => 'Your License Key(s) for Order #{order_number}',
		'purchase_email_content' => '<p>Thank you for your purchase!</p><p>Here are your license key(s):</p>{license_keys}<p>Please keep these keys safe.</p>'
	), $email_settings);
		// Sample data for testing
	$test_data = array(
		'order_number' => '12345',
		'customer_name' => 'Test Customer',
		'site_name' => get_bloginfo('name'),
		'license_keys' => '<ul style="list-style: none; padding-left: 0; margin-bottom: 20px;">
			<li style="margin-bottom: 10px; background-color: #f7f7f7; padding: 10px; border-radius: 4px;">
				<strong>' . __('Test Product', 'ridcodtoken') . ':</strong><br>
				<code style="font-size: 1.1em; background-color: #eee; padding: 3px 6px; border-radius: 3px; display: inline-block; margin-top: 5px;">TEST-1234-ABCD-5678</code>
			</li>
		</ul>'
	);
	
	// Replace placeholders in subject
	$subject = '[TEST] ' . str_replace(
		array('{order_number}', '{customer_name}', '{site_name}'),
		array($test_data['order_number'], $test_data['customer_name'], $test_data['site_name']),
		$email_settings['purchase_email_subject']
	);
		// Replace placeholders in content
	$body = str_replace(
		array('{license_keys}', '{order_number}', '{customer_name}', '{site_name}'),
		array($test_data['license_keys'], $test_data['order_number'], $test_data['customer_name'], $test_data['site_name']),
		$email_settings['purchase_email_content']
	);
	
	$headers = array('Content-Type: text/html; charset=UTF-8');
	wp_mail($to_email, $subject, $body, $headers);
}

/**
 * Sends a test expiry email
 */
function ridcodtoken_send_test_expiry_email($to_email) {
	$email_settings = get_option('ridcodtoken_email_settings', array());
	
	// Set default values if not set
	$email_settings = array_merge(array(
		'expiry_email_subject' => 'Your License Key is Expiring Soon',
		'expiry_email_content' => '<p>Your license key is expiring soon:</p><p><strong>License Key:</strong> {license_key}</p><p><strong>Expiry Date:</strong> {expiry_date}</p><p>Please renew your license to continue using our services.</p>'
	), $email_settings);
	
	// Sample data for testing
	$test_data = array(
		'license_key' => 'TEST-1234-ABCD-5678',
		'expiry_date' => date_i18n(get_option('date_format'), strtotime('+7 days')),
		'customer_name' => 'Test Customer',
		'site_name' => get_bloginfo('name')
	);
	
	// Replace placeholders in subject
	$subject = '[TEST] ' . str_replace(
		array('{license_key}', '{expiry_date}', '{customer_name}', '{site_name}'),
		array($test_data['license_key'], $test_data['expiry_date'], $test_data['customer_name'], $test_data['site_name']),
		$email_settings['expiry_email_subject']
	);
	
	// Replace placeholders in content
	$body = str_replace(
		array('{license_key}', '{expiry_date}', '{customer_name}', '{site_name}'),
		array($test_data['license_key'], $test_data['expiry_date'], $test_data['customer_name'], $test_data['site_name']),
		$email_settings['expiry_email_content']
	);
	
	$headers = array('Content-Type: text/html; charset=UTF-8');
	wp_mail($to_email, $subject, $body, $headers);
}