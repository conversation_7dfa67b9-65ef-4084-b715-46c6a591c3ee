<?php
/**
 * Database Migration Script for RIDCODTOKEN Product Security Update
 * 
 * هذا الملف يحدث قاعدة البيانات لدعم ربط التراخيص بالمنتجات
 * يجب تشغيله مرة واحدة فقط بعد تطبيق التحديثات
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit('Direct access not allowed');
}

/**
 * تشغيل ترقية قاعدة البيانات
 */
function ridcodtoken_run_product_security_migration() {
    global $wpdb;
    
    echo "<h2>🔄 بدء ترقية قاعدة البيانات لنظام الأمان الجديد</h2>";
    
    // 1. إضافة الجداول الجديدة
    ridcodtoken_add_new_tables();
    
    // 2. تحديث الجداول الموجودة
    ridcodtoken_update_existing_tables();
    
    // 3. ترقية التراخيص الموجودة
    ridcodtoken_migrate_existing_licenses();
    
    // 4. التحقق من سلامة البيانات
    ridcodtoken_verify_migration();
    
    echo "<h2>✅ تمت ترقية قاعدة البيانات بنجاح!</h2>";
}

/**
 * إضافة الجداول الجديدة
 */
function ridcodtoken_add_new_tables() {
    global $wpdb;
    
    echo "<h3>📋 إضافة الجداول الجديدة...</h3>";
    
    $charset_collate = $wpdb->get_charset_collate();
    
    // جدول ربط التراخيص بالمنتجات
    $table_license_products = $wpdb->prefix . 'ridcod_license_products';
    
    $sql = "CREATE TABLE IF NOT EXISTS $table_license_products (
        id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
        license_id bigint(20) UNSIGNED NOT NULL,
        product_id bigint(20) UNSIGNED NOT NULL,
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY license_product (license_id, product_id),
        KEY license_id (license_id),
        KEY product_id (product_id)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    echo "✅ تم إنشاء جدول $table_license_products<br>";
}

/**
 * تحديث الجداول الموجودة
 */
function ridcodtoken_update_existing_tables() {
    global $wpdb;
    
    echo "<h3>🔧 تحديث الجداول الموجودة...</h3>";
    
    $table_sites = $wpdb->prefix . 'ridcod_license_sites';
    
    // التحقق من وجود عمود product_id في جدول المواقع
    $column_exists = $wpdb->get_results("SHOW COLUMNS FROM $table_sites LIKE 'product_id'");
    
    if (empty($column_exists)) {
        $wpdb->query("ALTER TABLE $table_sites ADD COLUMN product_id bigint(20) UNSIGNED DEFAULT NULL AFTER license_id");
        $wpdb->query("ALTER TABLE $table_sites ADD KEY product_id (product_id)");
        echo "✅ تم إضافة عمود product_id إلى جدول $table_sites<br>";
    } else {
        echo "ℹ️ عمود product_id موجود بالفعل في جدول $table_sites<br>";
    }
}

/**
 * ترقية التراخيص الموجودة
 */
function ridcodtoken_migrate_existing_licenses() {
    global $wpdb;
    
    echo "<h3>🔄 ترقية التراخيص الموجودة...</h3>";
    
    $table_licenses = $wpdb->prefix . 'ridcod_licenses';
    
    // البحث عن التراخيص التي لا تحتوي على product_id
    $licenses_without_product = $wpdb->get_results(
        "SELECT id, license_key FROM $table_licenses WHERE product_id IS NULL OR product_id = 0"
    );
    
    if (empty($licenses_without_product)) {
        echo "ℹ️ جميع التراخيص مرتبطة بمنتجات بالفعل<br>";
        return;
    }
    
    echo "📊 تم العثور على " . count($licenses_without_product) . " ترخيص يحتاج للترقية<br>";
    
    // خيارات للترقية
    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
    echo "<h4>⚠️ تحديد المنتج للتراخيص الموجودة</h4>";
    echo "<p>يجب تحديد معرف المنتج للتراخيص الموجودة. اختر إحدى الطرق التالية:</p>";
    
    echo "<h5>الطريقة 1: تعيين منتج افتراضي</h5>";
    echo "<p>سيتم تعيين جميع التراخيص الموجودة للمنتج رقم 1 (يمكن تغييره لاحقاً)</p>";
    echo "<button onclick='migrateWithDefaultProduct()' class='button button-primary'>تطبيق المنتج الافتراضي (ID: 1)</button><br><br>";
    
    echo "<h5>الطريقة 2: تعيين يدوي</h5>";
    echo "<p>راجع كل ترخيص وحدد المنتج المناسب له يدوياً من لوحة الإدارة</p>";
    echo "<a href='" . admin_url('admin.php?page=ridcodtoken-license-manager') . "' class='button'>فتح إدارة التراخيص</a>";
    
    echo "</div>";
    
    // عرض التراخيص التي تحتاج للترقية
    echo "<h5>📋 التراخيص التي تحتاج للترقية:</h5>";
    echo "<table class='wp-list-table widefat fixed striped'>";
    echo "<thead><tr><th>ID</th><th>License Key</th><th>الإجراء المطلوب</th></tr></thead>";
    echo "<tbody>";
    
    foreach ($licenses_without_product as $license) {
        echo "<tr>";
        echo "<td>" . $license->id . "</td>";
        echo "<td>" . substr($license->license_key, 0, 8) . "****" . substr($license->license_key, -8) . "</td>";
        echo "<td>يحتاج لتحديد معرف المنتج</td>";
        echo "</tr>";
    }
    
    echo "</tbody></table>";
}

/**
 * تطبيق المنتج الافتراضي للتراخيص الموجودة
 */
function ridcodtoken_apply_default_product($product_id = 1) {
    global $wpdb;
    
    $table_licenses = $wpdb->prefix . 'ridcod_licenses';
    
    $updated = $wpdb->query($wpdb->prepare(
        "UPDATE $table_licenses SET product_id = %d WHERE product_id IS NULL OR product_id = 0",
        $product_id
    ));
    
    if ($updated !== false) {
        echo "✅ تم تحديث $updated ترخيص بالمنتج الافتراضي (ID: $product_id)<br>";
        return true;
    } else {
        echo "❌ فشل في تحديث التراخيص<br>";
        return false;
    }
}

/**
 * التحقق من سلامة البيانات بعد الترقية
 */
function ridcodtoken_verify_migration() {
    global $wpdb;
    
    echo "<h3>🔍 التحقق من سلامة البيانات...</h3>";
    
    $table_licenses = $wpdb->prefix . 'ridcod_licenses';
    $table_sites = $wpdb->prefix . 'ridcod_license_sites';
    $table_license_products = $wpdb->prefix . 'ridcod_license_products';
    
    // التحقق من الجداول
    $tables_check = array(
        $table_licenses => 'جدول التراخيص',
        $table_sites => 'جدول المواقع المفعلة',
        $table_license_products => 'جدول ربط التراخيص بالمنتجات'
    );
    
    foreach ($tables_check as $table => $name) {
        $exists = $wpdb->get_var("SHOW TABLES LIKE '$table'");
        if ($exists) {
            echo "✅ $name موجود<br>";
        } else {
            echo "❌ $name غير موجود<br>";
        }
    }
    
    // التحقق من الأعمدة
    $column_exists = $wpdb->get_results("SHOW COLUMNS FROM $table_sites LIKE 'product_id'");
    if (!empty($column_exists)) {
        echo "✅ عمود product_id موجود في جدول المواقع<br>";
    } else {
        echo "❌ عمود product_id غير موجود في جدول المواقع<br>";
    }
    
    // إحصائيات
    $total_licenses = $wpdb->get_var("SELECT COUNT(*) FROM $table_licenses");
    $licenses_with_product = $wpdb->get_var("SELECT COUNT(*) FROM $table_licenses WHERE product_id IS NOT NULL AND product_id > 0");
    
    echo "<h4>📊 إحصائيات:</h4>";
    echo "• إجمالي التراخيص: $total_licenses<br>";
    echo "• التراخيص المرتبطة بمنتجات: $licenses_with_product<br>";
    
    if ($total_licenses == $licenses_with_product) {
        echo "✅ جميع التراخيص مرتبطة بمنتجات<br>";
    } else {
        $remaining = $total_licenses - $licenses_with_product;
        echo "⚠️ يوجد $remaining ترخيص غير مرتبط بمنتج<br>";
    }
}

// JavaScript للتفاعل
?>
<script>
function migrateWithDefaultProduct() {
    if (confirm('هل أنت متأكد من تعيين المنتج الافتراضي (ID: 1) لجميع التراخيص الموجودة؟\n\nيمكنك تغيير هذا لاحقاً من لوحة الإدارة.')) {
        // إرسال طلب AJAX لتطبيق المنتج الافتراضي
        jQuery.post(ajaxurl, {
            action: 'ridcodtoken_apply_default_product',
            nonce: '<?php echo wp_create_nonce("ridcodtoken_migration"); ?>'
        }, function(response) {
            if (response.success) {
                alert('تم تطبيق المنتج الافتراضي بنجاح!');
                location.reload();
            } else {
                alert('حدث خطأ: ' + response.data);
            }
        });
    }
}
</script>

<?php
// إضافة معالج AJAX
add_action('wp_ajax_ridcodtoken_apply_default_product', function() {
    if (!wp_verify_nonce($_POST['nonce'], 'ridcodtoken_migration')) {
        wp_send_json_error('فشل التحقق من الأمان');
    }
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error('ليس لديك صلاحية لهذا الإجراء');
    }
    
    $result = ridcodtoken_apply_default_product(1);
    
    if ($result) {
        wp_send_json_success('تم تطبيق المنتج الافتراضي بنجاح');
    } else {
        wp_send_json_error('فشل في تطبيق المنتج الافتراضي');
    }
});

// تشغيل الترقية إذا تم طلبها
if (isset($_GET['run_migration']) && $_GET['run_migration'] == '1' && current_user_can('manage_options')) {
    ridcodtoken_run_product_security_migration();
}
?>
