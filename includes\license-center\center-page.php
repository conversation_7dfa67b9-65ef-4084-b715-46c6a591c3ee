<?php
/**
 * صفحة مركز التراخيص الموحد
 * 
 * @package RIDCODTOKEN
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap ridcodtoken-license-center">
    <h1 class="wp-heading-inline">
        🏠 RIDCODTOKEN License Center
        <span class="title-count">(<?php echo count($plugins); ?> plugins)</span>
    </h1>
    
    <hr class="wp-header-end">
    
    <!-- نظرة عامة سريعة -->
    <div class="ridcodtoken-overview">
        <div class="stats-grid">
            <div class="stat-box total">
                <div class="stat-icon">
                    <span class="dashicons dashicons-admin-plugins"></span>
                </div>
                <div class="stat-content">
                    <h3><?php echo count($plugins); ?></h3>
                    <p>Total Plugins</p>
                </div>
            </div>
            
            <div class="stat-box active">
                <div class="stat-icon">
                    <span class="dashicons dashicons-yes-alt"></span>
                </div>
                <div class="stat-content">
                    <h3><?php echo $active_count; ?></h3>
                    <p>Active Licenses</p>
                </div>
            </div>
            
            <div class="stat-box inactive">
                <div class="stat-icon">
                    <span class="dashicons dashicons-warning"></span>
                </div>
                <div class="stat-content">
                    <h3><?php echo $inactive_count; ?></h3>
                    <p>Need Activation</p>
                </div>
            </div>
            
            <div class="stat-box actions">
                <div class="stat-content">
                    <button type="button" class="button button-secondary" id="check-all-licenses">
                        <span class="dashicons dashicons-update"></span>
                        Check All
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <?php if ($inactive_count > 0): ?>
    <!-- تفعيل سريع -->
    <div class="ridcodtoken-quick-activation">
        <h2>🔑 Quick License Activation</h2>
        <div class="quick-form-container">
            <form id="ridcodtoken-quick-form">
                <div class="form-row">
                    <div class="form-field license-field">
                        <label for="license-key">License Key</label>
                        <input type="text" id="license-key" class="regular-text" placeholder="Enter your license key" required />
                    </div>
                    
                    <div class="form-field plugin-field">
                        <label for="target-plugin">Target Plugin</label>
                        <select id="target-plugin">
                            <option value="">Auto-detect from license</option>
                            <?php foreach ($plugins as $plugin): ?>
                                <?php if ($plugin['license_status'] !== 'active'): ?>
                                <option value="<?php echo esc_attr($plugin['slug']); ?>" data-product="<?php echo esc_attr($plugin['product_id']); ?>">
                                    <?php echo esc_html($plugin['name']); ?>
                                    <?php if ($plugin['product_id']): ?>
                                        (Product #<?php echo $plugin['product_id']; ?>)
                                    <?php endif; ?>
                                </option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-field submit-field">
                        <button type="submit" class="button button-primary">
                            <span class="dashicons dashicons-admin-network"></span>
                            Activate License
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <div id="quick-activation-result" class="activation-result" style="display: none;"></div>
    </div>
    <?php endif; ?>
    
    <!-- قائمة الإضافات -->
    <div class="ridcodtoken-plugins-list">
        <h2>📋 Installed RIDCODTOKEN Plugins</h2>
        
        <?php if (empty($plugins)): ?>
            <div class="no-plugins-found">
                <div class="notice notice-info">
                    <p>
                        <strong>No RIDCODTOKEN plugins found.</strong><br>
                        Install some RIDCODTOKEN protected plugins to manage their licenses here.
                    </p>
                </div>
            </div>
        <?php else: ?>
            <div class="plugins-grid">
                <?php foreach ($plugins as $plugin): ?>
                    <div class="plugin-card <?php echo $plugin['license_status']; ?> <?php echo $plugin['is_active'] ? 'plugin-active' : 'plugin-inactive'; ?>">
                        <div class="plugin-header">
                            <div class="plugin-icon">
                                <?php if ($plugin['license_status'] === 'active'): ?>
                                    <span class="dashicons dashicons-yes-alt"></span>
                                <?php else: ?>
                                    <span class="dashicons dashicons-lock"></span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="plugin-info">
                                <h3 class="plugin-title"><?php echo esc_html($plugin['name']); ?></h3>
                                <div class="plugin-meta">
                                    <span class="version">v<?php echo esc_html($plugin['version']); ?></span>
                                    <?php if ($plugin['product_id']): ?>
                                        <span class="product-id">Product #<?php echo $plugin['product_id']; ?></span>
                                    <?php endif; ?>
                                    <span class="plugin-status-text <?php echo $plugin['is_active'] ? 'active' : 'inactive'; ?>">
                                        <?php echo $plugin['is_active'] ? 'Plugin Active' : 'Plugin Inactive'; ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="plugin-description">
                            <p><?php echo esc_html(wp_trim_words($plugin['description'], 15)); ?></p>
                        </div>
                        
                        <div class="plugin-license-status">
                            <?php if ($plugin['license_status'] === 'active'): ?>
                                <div class="license-active">
                                    <span class="status-badge active">✅ License Active</span>
                                    <?php if ($plugin['last_check']): ?>
                                        <small class="last-check">
                                            Last checked: <?php echo human_time_diff($plugin['last_check']); ?> ago
                                        </small>
                                    <?php endif; ?>
                                    <?php if (!empty($plugin['license_key'])): ?>
                                        <small class="license-key">
                                            Key: <?php echo substr($plugin['license_key'], 0, 8) . '****' . substr($plugin['license_key'], -8); ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="license-inactive">
                                    <span class="status-badge inactive">❌ License Required</span>
                                    <small class="license-required">This plugin requires a valid license to function.</small>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="plugin-actions">
                            <?php if ($plugin['license_status'] === 'active'): ?>
                                <button type="button" class="button button-secondary check-license-btn" 
                                        data-plugin="<?php echo esc_attr($plugin['slug']); ?>"
                                        data-product="<?php echo esc_attr($plugin['product_id']); ?>">
                                    <span class="dashicons dashicons-update"></span>
                                    Check License
                                </button>
                                
                                <button type="button" class="button button-link-delete deactivate-license-btn" 
                                        data-plugin="<?php echo esc_attr($plugin['slug']); ?>">
                                    <span class="dashicons dashicons-dismiss"></span>
                                    Deactivate
                                </button>
                            <?php else: ?>
                                <button type="button" class="button button-primary activate-license-btn" 
                                        data-plugin="<?php echo esc_attr($plugin['slug']); ?>"
                                        data-product="<?php echo esc_attr($plugin['product_id']); ?>"
                                        data-name="<?php echo esc_attr($plugin['name']); ?>">
                                    <span class="dashicons dashicons-admin-network"></span>
                                    Activate License
                                </button>
                            <?php endif; ?>
                            
                            <?php if (!$plugin['is_active']): ?>
                                <a href="<?php echo admin_url('plugins.php'); ?>" class="button button-secondary">
                                    <span class="dashicons dashicons-admin-plugins"></span>
                                    Enable Plugin
                                </a>
                            <?php endif; ?>
                        </div>
                        
                        <div class="plugin-result" style="display: none;"></div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- معلومات إضافية -->
    <div class="ridcodtoken-info-section">
        <div class="info-grid">
            <div class="info-card">
                <h3>💡 How it works</h3>
                <ul>
                    <li>Each plugin requires a specific license for its product</li>
                    <li>Licenses are validated against the RIDCODTOKEN server</li>
                    <li>Invalid or expired licenses will disable plugin functionality</li>
                    <li>You can manage all licenses from this central location</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3>🔧 Troubleshooting</h3>
                <ul>
                    <li><strong>License not working?</strong> Check if it's for the correct product</li>
                    <li><strong>Plugin not detected?</strong> Make sure it includes RIDCODTOKEN SDK</li>
                    <li><strong>Connection issues?</strong> Check your internet connection</li>
                    <li><strong>Need help?</strong> Contact support with your license key</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تفعيل الترخيص -->
<div id="license-activation-modal" class="ridcodtoken-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Activate License</h3>
            <button type="button" class="modal-close">&times;</button>
        </div>
        
        <div class="modal-body">
            <form id="modal-activation-form">
                <div class="form-field">
                    <label for="modal-license-key">License Key</label>
                    <input type="text" id="modal-license-key" class="regular-text" placeholder="Enter license key" required />
                </div>
                
                <div class="form-field">
                    <label for="modal-plugin-name">Plugin</label>
                    <input type="text" id="modal-plugin-name" class="regular-text" readonly />
                    <input type="hidden" id="modal-plugin-slug" />
                    <input type="hidden" id="modal-product-id" />
                </div>
                
                <div class="modal-result" style="display: none;"></div>
            </form>
        </div>
        
        <div class="modal-footer">
            <button type="button" class="button button-secondary modal-close">Cancel</button>
            <button type="submit" form="modal-activation-form" class="button button-primary">Activate License</button>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // متغيرات عامة
    const messages = ridcodtoken_center.messages;
    
    // تفعيل سريع
    $('#ridcodtoken-quick-form').on('submit', function(e) {
        e.preventDefault();
        
        const licenseKey = $('#license-key').val().trim();
        const targetPlugin = $('#target-plugin').val();
        const productId = $('#target-plugin option:selected').data('product');
        
        if (!licenseKey) {
            showResult('#quick-activation-result', 'error', 'Please enter a license key');
            return;
        }
        
        activateLicense(licenseKey, targetPlugin, productId, '#quick-activation-result');
    });
    
    // تفعيل من بطاقة الإضافة
    $('.activate-license-btn').on('click', function() {
        const plugin = $(this).data('plugin');
        const productId = $(this).data('product');
        const pluginName = $(this).data('name');
        
        $('#modal-plugin-name').val(pluginName);
        $('#modal-plugin-slug').val(plugin);
        $('#modal-product-id').val(productId);
        $('#modal-license-key').val('');
        $('.modal-result').hide();
        
        $('#license-activation-modal').show();
        $('#modal-license-key').focus();
    });
    
    // تفعيل من النافذة المنبثقة
    $('#modal-activation-form').on('submit', function(e) {
        e.preventDefault();
        
        const licenseKey = $('#modal-license-key').val().trim();
        const pluginSlug = $('#modal-plugin-slug').val();
        const productId = $('#modal-product-id').val();
        
        if (!licenseKey) {
            showResult('.modal-result', 'error', 'Please enter a license key');
            return;
        }
        
        activateLicense(licenseKey, pluginSlug, productId, '.modal-result', function() {
            setTimeout(() => {
                $('#license-activation-modal').hide();
                location.reload();
            }, 2000);
        });
    });
    
    // إلغاء تفعيل الترخيص
    $('.deactivate-license-btn').on('click', function() {
        if (!confirm(messages.confirm_deactivate)) {
            return;
        }
        
        const plugin = $(this).data('plugin');
        const resultContainer = $(this).closest('.plugin-card').find('.plugin-result');
        
        deactivateLicense(plugin, resultContainer);
    });
    
    // فحص جميع التراخيص
    $('#check-all-licenses').on('click', function() {
        checkAllLicenses();
    });
    
    // إغلاق النافذة المنبثقة
    $('.modal-close').on('click', function() {
        $('#license-activation-modal').hide();
    });
    
    // إغلاق النافذة عند النقر خارجها
    $(window).on('click', function(e) {
        if ($(e.target).is('#license-activation-modal')) {
            $('#license-activation-modal').hide();
        }
    });
    
    // دوال مساعدة
    function activateLicense(licenseKey, pluginSlug, productId, resultContainer, callback) {
        showResult(resultContainer, 'loading', messages.activating);
        
        $.ajax({
            url: ridcodtoken_center.ajax_url,
            type: 'POST',
            data: {
                action: 'ridcodtoken_unified_activate',
                nonce: ridcodtoken_center.nonce,
                license_key: licenseKey,
                plugin_slug: pluginSlug,
                product_id: productId
            },
            success: function(response) {
                if (response.success) {
                    showResult(resultContainer, 'success', response.data.message);
                    if (callback) callback();
                } else {
                    showResult(resultContainer, 'error', response.data.message);
                }
            },
            error: function() {
                showResult(resultContainer, 'error', 'Connection error occurred');
            }
        });
    }
    
    function deactivateLicense(pluginSlug, resultContainer) {
        showResult(resultContainer, 'loading', messages.deactivating);
        
        $.ajax({
            url: ridcodtoken_center.ajax_url,
            type: 'POST',
            data: {
                action: 'ridcodtoken_unified_deactivate',
                nonce: ridcodtoken_center.nonce,
                plugin_slug: pluginSlug
            },
            success: function(response) {
                if (response.success) {
                    showResult(resultContainer, 'success', response.data.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showResult(resultContainer, 'error', response.data.message);
                }
            },
            error: function() {
                showResult(resultContainer, 'error', 'Connection error occurred');
            }
        });
    }
    
    function checkAllLicenses() {
        $('#check-all-licenses').prop('disabled', true).text('Checking...');
        
        $.ajax({
            url: ridcodtoken_center.ajax_url,
            type: 'POST',
            data: {
                action: 'ridcodtoken_unified_check_all',
                nonce: ridcodtoken_center.nonce
            },
            success: function(response) {
                $('#check-all-licenses').prop('disabled', false).html('<span class="dashicons dashicons-update"></span> Check All');
                
                if (response.success) {
                    // عرض النتائج أو إعادة تحميل الصفحة
                    location.reload();
                } else {
                    alert('Error checking licenses');
                }
            },
            error: function() {
                $('#check-all-licenses').prop('disabled', false).html('<span class="dashicons dashicons-update"></span> Check All');
                alert('Connection error occurred');
            }
        });
    }
    
    function showResult(container, type, message) {
        const $container = $(container);
        $container.removeClass('success error loading').addClass(type);
        $container.html('<p>' + message + '</p>').show();
        
        if (type === 'success' || type === 'error') {
            setTimeout(() => $container.fadeOut(), 5000);
        }
    }
});
</script>
