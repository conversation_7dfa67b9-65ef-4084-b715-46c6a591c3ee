jQuery(document).ready(function($) {
    'use strict';

    const $button = $('#ridcodtoken-admin-activate-button');
    const $input = $('#ridcodtoken-license-key-input');
    const $messageArea = $('#ridcodtoken-admin-message-area');
    const $messageText = $messageArea.find('.ridcodtoken-message-text');
    const $spinner = $button.siblings('.spinner');
    const $nonceField = $('#_ridcodtoken_save_nonce_admin'); // Nonce for saving

    // Ensure elements exist before binding event
    if ($button.length === 0) {
        return;
    }

    $button.on('click', function(e) {
        e.preventDefault();
        const licenseKey = $input.val().trim();

        // Basic validation
        if (licenseKey === '') {
            displayMessage(ridcodtoken_admin_activate_params.text_error_invalid_key, 'error');
            return;
        }

        // Disable button and show spinner
        $button.prop('disabled', true);
        $spinner.addClass('is-active');
        displayMessage(ridcodtoken_admin_activate_params.text_activating, 'loading');

        // --- Call REST API to Activate ---
        fetch(ridcodtoken_admin_activate_params.rest_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': ridcodtoken_admin_activate_params.nonce // REST API Nonce
            },
            body: JSON.stringify({
                license_key: licenseKey,
                site_url: ridcodtoken_admin_activate_params.site_url
            })
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(errData => {
                    let error = new Error(errData.message || ridcodtoken_admin_activate_params.text_error_general);
                    error.code = errData.code || 'fetch_error';
                    throw error;
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success && (data.code === 'activated' || data.code === 'already_active')) {
                // --- Call WordPress AJAX to Save Key ---
                saveLicenseKey(licenseKey);
                // Message will be updated by saveLicenseKey success/error
            } else {
                displayMessage(data.message || ridcodtoken_admin_activate_params.text_error_general, 'error');
                resetForm();
            }
        })
        .catch(error => {
            console.error('Activation Error:', error);
            displayMessage(error.message || ridcodtoken_admin_activate_params.text_error_general, 'error');
            resetForm();
        });
    });

    /**
     * Saves the license key via WordPress AJAX.
     */
    function saveLicenseKey(keyToSave) {
        const saveNonce = $nonceField.val();

        $.ajax({
            url: ridcodtoken_admin_activate_params.ajax_url,
            type: 'POST',
            data: {
                action: ridcodtoken_admin_activate_params.save_action, // 'ridcodtoken_save_license'
                license_key: keyToSave,
                _ajax_nonce: saveNonce
            },
            success: function(response) {
                if (response.success) {
                    // Key saved successfully. Reload page to show active state.
                    displayMessage(ridcodtoken_admin_activate_params.text_activated, 'success');
                    // Reload after a short delay
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    console.error('Failed to save license key:', response.data.message);
                    displayMessage(response.data.message || 'Failed to save license key.', 'error');
                    resetForm();
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error('AJAX Error:', textStatus, errorThrown);
                displayMessage('Error communicating with server to save key.', 'error');
                resetForm();
            }
        });
    }

    /**
     * Displays messages to the user.
     */
    function displayMessage(message, type) {
        $messageText.html(message); // Use html() if message contains HTML
        // Add appropriate class for styling (e.g., notice-success, notice-error)
        $messageArea.removeClass('notice-success notice-error notice-warning notice-info').addClass('notice-' + (type === 'success' ? 'success' : (type === 'error' ? 'error' : 'info'))).show();
        if (type !== 'loading') {
            $spinner.removeClass('is-active');
        } else {
             $messageArea.removeClass('notice-success notice-error').addClass('notice-info'); // Use info style for loading
        }
    }

    /**
     * Resets the form state.
     */
    function resetForm() {
        $button.prop('disabled', false);
        $spinner.removeClass('is-active');
    }
});