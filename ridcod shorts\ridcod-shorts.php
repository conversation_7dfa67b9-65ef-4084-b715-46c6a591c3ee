<?php
/**
 * Plugin Name: Ridcod Shorts - اختصار الروابط
 * Plugin URI: https://ridcod.com
 * Description: إضافة بسيطة لاختصار الروابط مع خيارات متعددة للتحويل المباشر أو عبر صفحة وسيطة مع عداد تنازلي (محمية بنظام التراخيص)
 * Version: 1.0.0
 * Author: Ridcod
 * Author URI: https://ridcod.com
 * Text Domain: ridcod-shorts
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * License: GPL v2 or later
 */

// منع الوصول المباشر للملف
if (!defined('ABSPATH')) {
    exit;
}

// تعريف الثوابت
define('RIDCOD_SHORTS_VERSION', '1.0.0');
define('RIDCOD_SHORTS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('RIDCOD_SHORTS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('RIDCOD_SHORTS_TABLE_NAME', 'ridcod_shorts');

// تحميل SDK نظام التراخيص
require_once RIDCOD_SHORTS_PLUGIN_DIR . 'sdk/ridcodtoken-plugin-sdk.php';

// إعداد نظام التراخيص
$ridcod_shorts_license_config = array(
    'plugin_file' => __FILE__,
    'plugin_name' => 'Ridcod Shorts',
    'plugin_slug' => 'ridcod-shorts',
    'api_url' => 'https://ridcod.com/', // يجب تغيير هذا إلى URL الـ API الحقيقي
    'product_id' => 222, // معرف المنتج في WooCommerce
    'version' => RIDCOD_SHORTS_VERSION
);

// إنشاء كائن نظام التراخيص
$ridcod_shorts_license_manager = new RIDCODTOKEN_Plugin_SDK($ridcod_shorts_license_config);

/**
 * فحص ما إذا كان الترخيص مفعل
 */
function ridcod_shorts_is_license_active() {
    $license_key = get_option('ridcodtoken_ridcod-shorts_license_key', '');
    $license_status = get_option('ridcodtoken_ridcod-shorts_license_status', 'inactive');
    return (!empty($license_key) && $license_status === 'active');
}

// إذا لم يكن الترخيص مفعل، لا نحمل باقي الإضافة
if (!ridcod_shorts_is_license_active()) {
    return;
}

/**
 * الكلاس الرئيسي للإضافة
 */
class RidcodShorts {
    
    /**
     * مدير التراخيص
     */
    private $license_manager;
    
    public function __construct() {
        // التحقق من وجود الترخيص قبل تحميل أي شيء
        if (!ridcod_shorts_is_license_active()) {
            // إضافة hook لعرض إشعار عدم وجود ترخيص
            add_action('admin_notices', array($this, 'show_license_notice'));
            return;
        }
        
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_uninstall_hook(__FILE__, array('RidcodShorts', 'uninstall'));
    }
    
    /**
     * عرض إشعار عدم وجود ترخيص
     */
    public function show_license_notice() {
        $plugin_name = 'Ridcod Shorts';
        $license_page_url = admin_url('admin.php?page=ridcodtoken-license-ridcod-shorts');
        
        echo '<div class="notice notice-error is-dismissible">';
        echo '<p><strong>' . esc_html($plugin_name) . '</strong>: ';
        echo 'هذه الإضافة تتطلب ترخيص صحيح للعمل. ';
        echo '<a href="' . esc_url($license_page_url) . '">انقر هنا لتفعيل الترخيص</a>';
        echo '</p>';
        echo '</div>';
    }
    
    /**
     * تهيئة الإضافة
     */
    public function init() {
        // فحص وترقية قاعدة البيانات إذا لزم الأمر
        $this->upgrade_database();
        
        // فحص وتحديث قواعد الروابط
        $this->check_rewrite_rules();
        
        // إضافة قائمة الإدارة
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // تسجيل الـ AJAX actions
        add_action('wp_ajax_create_short_url', array($this, 'create_short_url'));
        add_action('wp_ajax_delete_short_url', array($this, 'delete_short_url'));
        add_action('wp_ajax_get_short_urls_list', array($this, 'get_short_urls_list'));
        add_action('wp_ajax_get_stats_data', array($this, 'get_stats_data'));
        add_action('wp_ajax_force_rewrite_rules_update', array($this, 'force_rewrite_rules_update'));
        
        // معالجة الروابط المختصرة
        add_action('template_redirect', array($this, 'handle_short_url'));
        add_action('template_redirect', array($this, 'test_rewrite_rules'));
        add_action('parse_request', array($this, 'parse_request'));

        add_action('template_redirect', array($this, 'fallback_short_url_handler'), 5);
        add_action('wp', array($this, 'early_short_url_handler'), 1);
        
        // تحميل الأنماط والسكريبتات
        add_action('admin_enqueue_scripts', array($this, 'admin_scripts'));
        add_action('wp_enqueue_scripts', array($this, 'frontend_scripts'));
        
        // إضافة العداد التنازلي للصفحات الوسيطة
        add_filter('the_content', array($this, 'add_countdown_to_content'), 1);
        
        // إضافة rewrite rules و query vars
        add_filter('query_vars', array($this, 'add_query_vars'));
        $this->add_rewrite_rules();
        
        // تحديث قواعد الروابط فوراً إذا لم تكن تعمل
        if (!get_option('ridcod_shorts_rules_updated')) {
            $this->add_rewrite_rules();
            flush_rewrite_rules();
            update_option('ridcod_shorts_rules_updated', true);
        }

        add_action('wp_loaded', array($this, 'check_rewrite_rules_on_load'));

        // التحقق من الحاجة لتحديث قواعد الروابط للإصدار الجديد
        $current_rules_version = get_option('ridcod_shorts_rules_version', '1.0.0');
        if (version_compare($current_rules_version, '3.0.0', '<')) {
            $this->add_rewrite_rules();
            flush_rewrite_rules();
            update_option('ridcod_shorts_rules_version', '3.0.0');
        }
        
        // جدولة التحقق الأسبوعي من الترخيص
        $this->schedule_weekly_license_check();
    }
    
    /**
     * جدولة التحقق الأسبوعي من الترخيص
     */
    private function schedule_weekly_license_check() {
        $hook_name = 'ridcod_shorts_weekly_license_check';
        
        if (!wp_next_scheduled($hook_name)) {
            wp_schedule_event(time(), 'weekly', $hook_name);
        }
        
        // إضافة معالج للحدث المجدول
        add_action($hook_name, array($this, 'check_license_weekly'));
    }
    
    /**
     * التحقق الأسبوعي من صحة الترخيص
     */
    public function check_license_weekly() {
        $license_key = get_option('ridcodtoken_ridcod-shorts_license_key', '');
        $api_url = 'https://api.ridcod.com/'; // يجب تغيير هذا إلى URL الـ API الحقيقي
        
        if (empty($license_key) || empty($api_url)) {
            return;
        }
        
        $api_endpoint = trailingslashit($api_url) . 'wp-json/ridcodtoken/v1/status';
        
        $response = wp_remote_get($api_endpoint . '?' . http_build_query(array(
            'license_key' => $license_key,
            'site_url' => home_url(),
            'product_id' => 222 // معرف المنتج
        )), array(
            'timeout' => 30,
            'headers' => array(
                'User-Agent' => 'RIDCODTOKEN-SDK/' . RIDCOD_SHORTS_VERSION
            )
        ));
        
        if (is_wp_error($response)) {
            // في حالة فشل الاتصال، نتجاهل للمرة الواحدة
            return;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (!$data) {
            return;
        }
        
        // فحص حالة الترخيص
        $is_valid = false;
        if (isset($data['status']) && $data['status'] === 'active' &&
            isset($data['site_is_active']) && $data['site_is_active'] === true &&
            (!isset($data['is_expired']) || $data['is_expired'] !== true)) {
            $is_valid = true;
        }
        
        // تحديث حالة الترخيص
        if ($is_valid) {
            update_option('ridcodtoken_ridcod-shorts_license_status', 'active');
        } else {
            update_option('ridcodtoken_ridcod-shorts_license_status', 'inactive');
        }
        
        // تحديث وقت آخر فحص
        update_option('ridcodtoken_ridcod-shorts_last_check', time());
    }
    
    /**
     * الحصول على إعدادات الإضافة
     */
    public function get_settings() {
        return get_option('ridcod_shorts_settings', array(
            'default_countdown' => 5,
            'countdown_text' => __('سيتم عرض المحتوى خلال:', 'ridcod-shorts'),
            'continue_text' => __('متابعة', 'ridcod-shorts'),
            'redirect_text' => __('انقر على الزر أدناه للانتقال إلى الرابط المطلوب', 'ridcod-shorts'),
            'button_text' => __('الانتقال إلى الرابط', 'ridcod-shorts'),
            'countdown_color' => '#007cba',
            'button_color' => '#007cba',
            'background_color' => '#ffffff',
            'text_color' => '#333333'
        ));
    }
    
    /**
     * تحميل ملفات الترجمة
     */
    public function load_textdomain() {
        load_plugin_textdomain('ridcod-shorts', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    /**
     * تفعيل الإضافة
     */
    public function activate() {
        $this->create_tables();
        $this->upgrade_database();
        $this->add_rewrite_rules();
        flush_rewrite_rules();

        // تحديث الخيارات لضمان وجود الإعدادات
        update_option('ridcod_shorts_version', RIDCOD_SHORTS_VERSION);
        update_option('ridcod_shorts_rules_version', '3.0.0');
        delete_option('ridcod_shorts_rules_updated');
    }
    
    /**
     * إلغاء تفعيل الإضافة
     */
    public function deactivate() {
        flush_rewrite_rules();
        
        // إلغاء المهام المجدولة
        $hook_name = 'ridcod_shorts_weekly_license_check';
        $timestamp = wp_next_scheduled($hook_name);
        if ($timestamp) {
            wp_unschedule_event($timestamp, $hook_name);
        }
    }
    
    /**
     * حذف الإضافة
     */
    public static function uninstall() {
        global $wpdb;
        $table_name = $wpdb->prefix . RIDCOD_SHORTS_TABLE_NAME;
        $wpdb->query("DROP TABLE IF EXISTS $table_name");
        delete_option('ridcod_shorts_options');
    }
    
    /**
     * إنشاء جداول قاعدة البيانات
     */
    private function create_tables() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . RIDCOD_SHORTS_TABLE_NAME;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            short_code varchar(255) NOT NULL,
            original_url text NOT NULL,
            redirect_type enum('direct', 'intermediate') DEFAULT 'direct',
            intermediate_page_id varchar(255) DEFAULT NULL,
            countdown_seconds int(11) DEFAULT 5,
            clicks int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY short_code (short_code)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * إضافة قائمة الإدارة
     */
    public function add_admin_menu() {
        add_menu_page(
            'اختصار الروابط',
            'اختصار الروابط',
            'manage_options',
            'ridcod-shorts',
            array($this, 'admin_page'),
            'dashicons-admin-links',
            30
        );
        
        // إضافة صفحة الإعدادات كصفحة فرعية
        add_submenu_page(
            'ridcod-shorts',
            __('إعدادات اختصار الروابط', 'ridcod-shorts'),
            __('الإعدادات', 'ridcod-shorts'),
            'manage_options',
            'ridcod-shorts-settings',
            array($this, 'settings_page')
        );
    }
    
    /**
     * صفحة الإدارة
     */
    public function admin_page() {
        include RIDCOD_SHORTS_PLUGIN_DIR . 'includes/admin-page.php';
    }
    
    /**
     * صفحة الإعدادات
     */
    public function settings_page() {
        include RIDCOD_SHORTS_PLUGIN_DIR . 'includes/settings-page.php';
    }
    
    /**
     * تحميل السكريبتات في لوحة الإدارة
     */
    public function admin_scripts($hook) {
        if (strpos($hook, 'ridcod-shorts') === false) {
            return;
        }

        // تأكد من تحميل jQuery
        wp_enqueue_script('jquery');

        wp_enqueue_script('ridcod-shorts-admin', RIDCOD_SHORTS_PLUGIN_URL . 'assets/js/admin.js', array('jquery'), RIDCOD_SHORTS_VERSION, true);
        wp_enqueue_style('ridcod-shorts-admin', RIDCOD_SHORTS_PLUGIN_URL . 'assets/css/admin.css', array(), RIDCOD_SHORTS_VERSION);

        wp_localize_script('ridcod-shorts-admin', 'ridcod_shorts_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ridcod_shorts_nonce'),
            'home_url' => home_url()
        ));
    }
    
    /**
     * تحميل السكريبتات في الواجهة الأمامية
     */
    public function frontend_scripts() {
        wp_enqueue_script('ridcod-shorts-frontend', RIDCOD_SHORTS_PLUGIN_URL . 'assets/js/frontend.js', array('jquery'), RIDCOD_SHORTS_VERSION, true);
        wp_enqueue_style('ridcod-shorts-frontend', RIDCOD_SHORTS_PLUGIN_URL . 'assets/css/frontend.css', array(), RIDCOD_SHORTS_VERSION);
        
        // إضافة CSS مخصص بناءً على الإعدادات
        $settings = $this->get_settings();
        $custom_css = "
            :root {
                --ridcod-countdown-color: {$settings['countdown_color']};
                --ridcod-button-color: {$settings['button_color']};
                --ridcod-background-color: {$settings['background_color']};
                --ridcod-text-color: {$settings['text_color']};
            }
        ";
        
        wp_add_inline_style('ridcod-shorts-frontend', $custom_css);
    }
    
    /**
     * إضافة rewrite rules
     */
    public function add_rewrite_rules() {
        add_rewrite_rule('^([a-zA-Z0-9]{3,10})/?$', 'index.php?ridcod_short=$matches[1]', 'top');
        add_rewrite_rule('^([a-zA-Z0-9]{3,10})/(.*)/?$', 'index.php?ridcod_short=$matches[1]&ridcod_extra=$matches[2]', 'top');
    }
    
    /**
     * إضافة query vars
     */
    public function add_query_vars($vars) {
        $vars[] = 'ridcod_short';
        $vars[] = 'ridcod_extra';
        return $vars;
    }
    
    /**
     * معالجة الروابط المختصرة
     */
    public function handle_short_url() {
        $short_code = get_query_var('ridcod_short');

        if (!$short_code) {
            return;
        }

        $short_code = sanitize_text_field($short_code);

        global $wpdb;
        $table_name = $wpdb->prefix . RIDCOD_SHORTS_TABLE_NAME;

        $short_url = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE short_code = %s",
            $short_code
        ));

        if (!$short_url) {
            return;
        }

        // تحديث عداد النقرات
        $wpdb->update(
            $table_name,
            array('clicks' => $short_url->clicks + 1),
            array('id' => $short_url->id),
            array('%d'),
            array('%d')
        );

        if ($short_url->redirect_type === 'direct') {
            // التحويل المباشر
            wp_redirect($short_url->original_url, 301);
            exit;
        } else {
            // التحويل عبر صفحة وسيطة
            $this->show_intermediate_page($short_url);
            exit;
        }
    }
    
    /**
     * معالجة طلبات الروابط المختصرة (طريقة بديلة)
     */
    public function parse_request($wp) {
        // التحقق من أن الطلب يحتوي على رمز مختصر مباشر
        if (isset($wp->request) && preg_match('/^([a-zA-Z0-9]{3,10})\/?$/', $wp->request, $matches)) {
            $short_code = $matches[1];

            global $wpdb;
            $table_name = $wpdb->prefix . RIDCOD_SHORTS_TABLE_NAME;

            $short_url = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $table_name WHERE short_code = %s",
                $short_code
            ));

            if ($short_url) {
                $wpdb->update(
                    $table_name,
                    array('clicks' => $short_url->clicks + 1),
                    array('id' => $short_url->id),
                    array('%d'),
                    array('%d')
                );

                if ($short_url->redirect_type === 'direct') {
                    wp_redirect($short_url->original_url, 301);
                    exit;
                } else {
                    $this->show_intermediate_page($short_url);
                    exit;
                }
            } else {
                if ($this->is_wordpress_page($short_code)) {
                    return;
                }
            }
        }
    }

    /**
     * التحقق من أن الرمز ليس صفحة WordPress موجودة
     */
    private function is_wordpress_page($slug) {
        $reserved_slugs = array(
            'wp-admin', 'wp-content', 'wp-includes', 'wp-json',
            'admin', 'login', 'register', 'dashboard', 'profile',
            'feed', 'rdf', 'rss', 'rss2', 'atom', 'sitemap',
            'wp-login', 'wp-register', 'wp-cron', 'xmlrpc'
        );

        if (in_array($slug, $reserved_slugs)) {
            return true;
        }

        $page = get_page_by_path($slug, OBJECT, 'page');
        if ($page && $page->post_status === 'publish') {
            return true;
        }

        $post = get_page_by_path($slug, OBJECT, 'post');
        if ($post && $post->post_status === 'publish') {
            return true;
        }

        $custom_post_types = get_post_types(array('public' => true, '_builtin' => false));
        foreach ($custom_post_types as $post_type) {
            $custom_post = get_page_by_path($slug, OBJECT, $post_type);
            if ($custom_post && $custom_post->post_status === 'publish') {
                return true;
            }
        }

        return false;
    }
    
    /**
     * آلية احتياطية لمعالجة الروابط المختصرة
     */
    public function fallback_short_url_handler() {
        if (!is_404() && !is_main_query()) {
            return;
        }

        $request_uri = $_SERVER['REQUEST_URI'];
        $path = trim(parse_url($request_uri, PHP_URL_PATH), '/');
        $path_parts = explode('/', $path);
        $potential_code = $path_parts[0];

        if (preg_match('/^([a-zA-Z0-9]{3,10})$/', $potential_code, $matches)) {
            $short_code = $matches[1];

            global $wpdb;
            $table_name = $wpdb->prefix . RIDCOD_SHORTS_TABLE_NAME;

            $short_url = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $table_name WHERE short_code = %s",
                $short_code
            ));

            if ($short_url) {
                $wpdb->update(
                    $table_name,
                    array('clicks' => $short_url->clicks + 1),
                    array('id' => $short_url->id),
                    array('%d'),
                    array('%d')
                );

                status_header(301);
                nocache_headers();

                if ($short_url->redirect_type === 'direct') {
                    wp_redirect($short_url->original_url, 301);
                    exit;
                } else {
                    $this->show_intermediate_page($short_url);
                    exit;
                }
            }
        }
    }

    /**
     * معالجة مبكرة للروابط المختصرة
     */
    public function early_short_url_handler() {
        global $wp;

        if (isset($wp->request) && !empty($wp->request)) {
            $request_path = trim($wp->request, '/');

            if (preg_match('/^([a-zA-Z0-9]{3,10})(?:\/.*)?$/', $request_path, $matches)) {
                $short_code = $matches[1];

                global $wpdb;
                $table_name = $wpdb->prefix . RIDCOD_SHORTS_TABLE_NAME;

                $short_url = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $table_name WHERE short_code = %s",
                    $short_code
                ));

                if ($short_url) {
                    $wpdb->update(
                        $table_name,
                        array('clicks' => $short_url->clicks + 1),
                        array('id' => $short_url->id),
                        array('%d'),
                        array('%d')
                    );

                    if ($short_url->redirect_type === 'direct') {
                        wp_redirect($short_url->original_url, 301);
                        exit;
                    } else {
                        $this->show_intermediate_page($short_url);
                        exit;
                    }
                }
            }
        }
    }

    /**
     * عرض الصفحة الوسيطة
     */
    private function show_intermediate_page($short_url) {
        // الحصول على الإعدادات
        $settings = $this->get_settings();
        
        // التحقق من نوع الصفحة الوسيطة
        if ($short_url->intermediate_page_id === 'random_post') {
            // الحصول على مقال عشوائي
            $random_posts = get_posts(array(
                'numberposts' => 1,
                'post_status' => 'publish',
                'orderby' => 'rand',
                'post_type' => 'post'
            ));
            
            if (!empty($random_posts)) {
                $selected_post = $random_posts[0];
            } else {
                // إذا لم يوجد مقالات، استخدم التحويل المباشر
                wp_redirect($short_url->original_url, 301);
                exit;
            }
        } else {
            // الحصول على محتوى الصفحة الوسيطة العادية
            $selected_post = get_post($short_url->intermediate_page_id);
        }
        
        if (!$selected_post) {
            wp_redirect($short_url->original_url, 301);
            exit;
        }
        
        // حفظ بيانات التحويل في الجلسة أو متغيرات عامة
        global $ridcod_redirect_data;
        $ridcod_redirect_data = array(
            'original_url' => $short_url->original_url,
            'countdown_seconds' => $short_url->countdown_seconds,
            'short_code' => $short_url->short_code,
            'settings' => $settings
        );
        
        // إعادة توجيه إلى الصفحة/المقال مع معامل خاص
        $redirect_url = get_permalink($selected_post->ID) . '?ridcod_intermediate=1&ridcod_code=' . $short_url->short_code;
        wp_redirect($redirect_url);
        exit;
    }
    
    /**
     * إضافة العداد التنازلي للصفحات الوسيطة
     */
    public function add_countdown_to_content($content) {
        // التحقق من وجود معامل الصفحة الوسيطة
        if (!isset($_GET['ridcod_intermediate']) || !isset($_GET['ridcod_code'])) {
            return $content;
        }
        
        $short_code = sanitize_text_field($_GET['ridcod_code']);
        
        // الحصول على بيانات الرابط المختصر
        global $wpdb;
        $table_name = $wpdb->prefix . RIDCOD_SHORTS_TABLE_NAME;
        
        $short_url = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE short_code = %s",
            $short_code
        ));
        
        if (!$short_url) {
            return $content;
        }
        
        // الحصول على الإعدادات
        $settings = $this->get_settings();
        
        // إنشاء HTML للعداد التنازلي
        $countdown_html = $this->get_countdown_html($short_url, $settings);
        
        // إضافة العداد قبل المحتوى
        return $countdown_html . $content;
    }
    
    /**
     * إنشاء HTML للعداد التنازلي
     */
    private function get_countdown_html($short_url, $settings) {
        ob_start();
        ?>
        <!-- بانر العداد التنازلي أسفل الهيدر -->
        <div id="ridcod-countdown-banner" style="
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: <?php echo esc_attr($settings['countdown_color']); ?>;
            color: <?php echo esc_attr($settings['text_color']); ?>;
            padding: 20px;
            text-align: center;
            z-index: 999999;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        ">
            <div style="max-width: 800px; margin: 0 auto;">
                <h3 style="margin: 0 0 10px 0; color: <?php echo esc_attr($settings['text_color']); ?>;">مرحباً بك!</h3>
                <p style="margin: 0 0 15px 0; opacity: 0.9; color: <?php echo esc_attr($settings['text_color']); ?>;"><?php echo esc_html($settings['countdown_text']); ?></p>
                <div id="ridcod-countdown-timer" style="
                    font-size: 36px;
                    font-weight: bold;
                    color: <?php echo esc_attr($settings['text_color']); ?>;
                    margin: 10px 0;
                "><?php echo $short_url->countdown_seconds; ?></div>
                <button id="ridcod-scroll-to-content" style="
                    background: <?php echo esc_attr($settings['background_color']); ?>;
                    color: <?php echo esc_attr($settings['countdown_color']); ?>;
                    border: none;
                    padding: 12px 25px;
                    border-radius: 5px;
                    cursor: pointer;
                    display: none;
                    font-weight: bold;
                    margin-top: 10px;
                "><?php echo esc_html($settings['continue_text']); ?> ⬇️</button>
            </div>
        </div>
        
        <!-- زر الانتقال للرابط فوق الفوتر -->
        <div id="ridcod-redirect-footer" style="
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: <?php echo esc_attr($settings['button_color']); ?>;
            color: <?php echo esc_attr($settings['text_color']); ?>;
            padding: 20px;
            text-align: center;
            z-index: 999998;
            display: none;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
        ">
            <div style="max-width: 800px; margin: 0 auto;">
                <p style="margin: 0 0 15px 0; font-size: 18px; color: <?php echo esc_attr($settings['text_color']); ?>;"><?php echo esc_html($settings['redirect_text']); ?></p>
                <a href="<?php echo esc_url($short_url->original_url); ?>" style="
                    background: <?php echo esc_attr($settings['background_color']); ?>;
                    color: <?php echo esc_attr($settings['button_color']); ?>;
                    padding: 15px 30px;
                    text-decoration: none;
                    border-radius: 5px;
                    font-weight: bold;
                    font-size: 16px;
                    display: inline-block;
                    margin-right: 10px;
                "><?php echo esc_html($settings['button_text']); ?></a>
                <button onclick="document.getElementById('ridcod-redirect-footer').style.display='none'" style="
                    background: transparent;
                    color: <?php echo esc_attr($settings['text_color']); ?>;
                    border: 1px solid <?php echo esc_attr($settings['text_color']); ?>;
                    padding: 12px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 14px;
                ">إخفاء</button>
            </div>
        </div>
        
        <script>
        (function() {
            let countdown = <?php echo $short_url->countdown_seconds; ?>;
            const timerElement = document.getElementById('ridcod-countdown-timer');
            const scrollBtn = document.getElementById('ridcod-scroll-to-content');
            const topBanner = document.getElementById('ridcod-countdown-banner');
            const footerBanner = document.getElementById('ridcod-redirect-footer');
            
            // إضافة مساحة فارغة أعلى الصفحة لتجنب تداخل البانر مع المحتوى
            document.body.style.paddingTop = '120px';
            
            // بدء العداد التنازلي
            const timer = setInterval(() => {
                countdown--;
                timerElement.textContent = countdown;
                
                if (countdown <= 0) {
                    clearInterval(timer);
                    timerElement.textContent = '✅';
                    timerElement.parentNode.querySelector('p').textContent = '<?php echo esc_js($settings['continue_text']); ?>!';
                    scrollBtn.style.display = 'inline-block';
                }
            }, 1000);
            
            // عند النقر على زر التمرير للمحتوى
            scrollBtn.addEventListener('click', function() {
                // إخفاء البانر العلوي
                topBanner.style.display = 'none';
                document.body.style.paddingTop = '0';
                
                // التمرير إلى أسفل الصفحة (قبل الفوتر)
                window.scrollTo({
                    top: document.body.scrollHeight - window.innerHeight,
                    behavior: 'smooth'
                });
                
                // إظهار البانر السفلي فوراً عند الوصول
                setTimeout(() => {
                    footerBanner.style.display = 'block';
                }, 800); // وقت أقل للإظهار السريع
            });
            
            // إضافة مساحة فارغة أسفل الصفحة لتجنب تداخل البانر السفلي مع المحتوى
            const spacer = document.createElement('div');
            spacer.style.height = '100px';
            spacer.id = 'ridcod-footer-spacer';
            document.body.appendChild(spacer);
            
        })();
        </script>
        
        <style>
        /* تطبيق إعدادات التصميم */
        body {
            background-color: <?php echo esc_attr($settings['background_color']); ?> !important;
            color: <?php echo esc_attr($settings['text_color']); ?> !important;
        }
        
        /* تأكيد أن البانر يظهر فوق كل شيء */
        #ridcod-countdown-banner,
        #ridcod-redirect-footer {
            z-index: 999999 !important;
        }
        
        /* تحسين المظهر على الجوال */
        @media (max-width: 768px) {
            #ridcod-countdown-banner,
            #ridcod-redirect-footer {
                padding: 15px 10px;
            }
            
            #ridcod-countdown-timer {
                font-size: 28px !important;
            }
            
            #ridcod-scroll-to-content,
            #ridcod-redirect-footer a {
                width: 100%;
                display: block !important;
                margin: 10px 0 !important;
            }
        }
        </style>
        <?php
        return ob_get_clean();
    }
    
    /**
     * الحصول على جميع الروابط المختصرة
     */
    public static function get_all_short_urls() {
        global $wpdb;
        $table_name = $wpdb->prefix . RIDCOD_SHORTS_TABLE_NAME;
        
        return $wpdb->get_results("SELECT * FROM $table_name ORDER BY created_at DESC");
    }
    
    /**
     * إنشاء رابط مختصر جديد
     */
    public function create_short_url() {
        check_ajax_referer('ridcod_shorts_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('غير مسموح');
        }
        
        $original_url = sanitize_url($_POST['original_url']);
        $redirect_type = sanitize_text_field($_POST['redirect_type']);
        $short_code = sanitize_text_field($_POST['short_code']);
        $intermediate_page_id = isset($_POST['intermediate_page_id']) ? sanitize_text_field($_POST['intermediate_page_id']) : null;
        
        // استخدام العداد الافتراضي من الإعدادات
        $settings = $this->get_settings();
        $countdown_seconds = $settings['default_countdown'];
        
        // معالجة القيمة الخاصة للمقالات العشوائية
        if ($intermediate_page_id === 'random_post') {
            // الاحتفاظ بالقيمة النصية للمقالات العشوائية
            $intermediate_page_value = 'random_post';
        } else {
            // تحويل إلى رقم للصفحات العادية
            $intermediate_page_value = !empty($intermediate_page_id) ? intval($intermediate_page_id) : null;
        }
        
        // التحقق من صحة البيانات
        if (!filter_var($original_url, FILTER_VALIDATE_URL)) {
            wp_send_json_error('رابط غير صحيح');
        }
        
        // توليد رمز مختصر عشوائي إذا لم يتم تحديده
        if (empty($short_code)) {
            $short_code = $this->generate_random_code();
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . RIDCOD_SHORTS_TABLE_NAME;
        
        // التحقق من عدم وجود نفس الرمز
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE short_code = %s",
            $short_code
        ));
        
        if ($existing > 0) {
            wp_send_json_error('الرمز المختصر موجود بالفعل');
        }
        
        // إدراج البيانات
        $result = $wpdb->insert(
            $table_name,
            array(
                'short_code' => $short_code,
                'original_url' => $original_url,
                'redirect_type' => $redirect_type,
                'intermediate_page_id' => $intermediate_page_value,
                'countdown_seconds' => $countdown_seconds
            ),
            array('%s', '%s', '%s', '%s', '%d')
        );
        
        if ($result === false) {
            wp_send_json_error('خطأ في حفظ البيانات');
        }
        
        $short_url = home_url('/' . $short_code);

        wp_send_json_success(array(
            'short_url' => $short_url,
            'short_code' => $short_code
        ));
    }
    
    /**
     * حذف رابط مختصر
     */
    public function delete_short_url() {
        check_ajax_referer('ridcod_shorts_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('غير مسموح');
        }

        $id = intval($_POST['id']);

        global $wpdb;
        $table_name = $wpdb->prefix . RIDCOD_SHORTS_TABLE_NAME;

        $result = $wpdb->delete(
            $table_name,
            array('id' => $id),
            array('%d')
        );

        if ($result === false) {
            wp_send_json_error('خطأ في حذف البيانات');
        }

        wp_send_json_success('تم الحذف بنجاح');
    }

    /**
     * جلب قائمة الروابط المختصرة عبر AJAX
     */
    public function get_short_urls_list() {
        check_ajax_referer('ridcod_shorts_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('غير مسموح');
        }

        $short_urls = self::get_all_short_urls();

        ob_start();
        if (empty($short_urls)): ?>
            <div class="notice notice-info">
                <p><?php _e('لا توجد روابط مختصرة بعد. ابدأ بإنشاء أول رابط!', 'ridcod-shorts'); ?></p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th scope="col"><?php _e('الرمز المختصر', 'ridcod-shorts'); ?></th>
                            <th scope="col"><?php _e('الرابط الأصلي', 'ridcod-shorts'); ?></th>
                            <th scope="col"><?php _e('نوع التحويل', 'ridcod-shorts'); ?></th>
                            <th scope="col"><?php _e('عدد النقرات', 'ridcod-shorts'); ?></th>
                            <th scope="col"><?php _e('تاريخ الإنشاء', 'ridcod-shorts'); ?></th>
                            <th scope="col"><?php _e('الإجراءات', 'ridcod-shorts'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($short_urls as $url): ?>
                            <tr>
                                <td>
                                    <strong>
                                        <a href="<?php echo home_url('/' . $url->short_code); ?>" target="_blank">
                                            <?php echo esc_html($url->short_code); ?>
                                            <span class="dashicons dashicons-external"></span>
                                        </a>
                                    </strong>
                                    <br>
                                    <code><?php echo home_url('/' . $url->short_code); ?></code>
                                </td>
                                <td>
                                    <a href="<?php echo esc_url($url->original_url); ?>" target="_blank" title="<?php echo esc_attr($url->original_url); ?>">
                                        <?php echo esc_html(wp_trim_words($url->original_url, 8, '...')); ?>
                                    </a>
                                </td>
                                <td>
                                    <?php if ($url->redirect_type === 'direct'): ?>
                                        <span class="badge badge-direct"><?php _e('مباشر', 'ridcod-shorts'); ?></span>
                                    <?php else: ?>
                                        <span class="badge badge-intermediate"><?php _e('صفحة وسيطة', 'ridcod-shorts'); ?></span>
                                        <?php if ($url->intermediate_page_id === 'random_post'): ?>
                                            <br><small>🎲 <?php _e('مقال عشوائي', 'ridcod-shorts'); ?></small>
                                        <?php elseif ($url->intermediate_page_id): ?>
                                            <br><small><?php echo get_the_title($url->intermediate_page_id); ?></small>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?php echo number_format($url->clicks); ?></strong>
                                </td>
                                <td>
                                    <?php echo wp_date('Y/m/d H:i', strtotime($url->created_at)); ?>
                                </td>
                                <td>
                                    <button type="button" class="button button-small copy-url-btn" data-url="<?php echo home_url('/' . $url->short_code); ?>">
                                        <?php _e('نسخ', 'ridcod-shorts'); ?>
                                    </button>
                                    <button type="button" class="button button-small button-link-delete delete-url-btn" data-id="<?php echo $url->id; ?>">
                                        <?php _e('حذف', 'ridcod-shorts'); ?>
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif;

        $html = ob_get_clean();

        wp_send_json_success(array(
            'html' => $html,
            'count' => count($short_urls)
        ));
    }

    /**
     * جلب بيانات الإحصائيات عبر AJAX
     */
    public function get_stats_data() {
        check_ajax_referer('ridcod_shorts_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('غير مسموح');
        }

        $short_urls = self::get_all_short_urls();
        $total_urls = count($short_urls);
        $total_clicks = array_sum(array_column($short_urls, 'clicks'));
        $avg_clicks = $total_urls > 0 ? round($total_clicks / $total_urls, 2) : 0;

        ob_start();
        ?>
        <div class="stats-grid">
            <div class="stat-box">
                <div class="stat-icon">
                    <span class="dashicons dashicons-admin-links"></span>
                </div>
                <div class="stat-content">
                    <h3><?php echo number_format($total_urls); ?></h3>
                    <p><?php _e('إجمالي الروابط', 'ridcod-shorts'); ?></p>
                </div>
            </div>

            <div class="stat-box">
                <div class="stat-icon">
                    <span class="dashicons dashicons-chart-line"></span>
                </div>
                <div class="stat-content">
                    <h3><?php echo number_format($total_clicks); ?></h3>
                    <p><?php _e('إجمالي النقرات', 'ridcod-shorts'); ?></p>
                </div>
            </div>

            <div class="stat-box">
                <div class="stat-icon">
                    <span class="dashicons dashicons-chart-bar"></span>
                </div>
                <div class="stat-content">
                    <h3><?php echo $avg_clicks; ?></h3>
                    <p><?php _e('متوسط النقرات', 'ridcod-shorts'); ?></p>
                </div>
            </div>
        </div>

        <?php if (!empty($short_urls)): ?>
            <h3><?php _e('أكثر الروابط استخداماً', 'ridcod-shorts'); ?></h3>
            <div class="top-urls">
                <?php
                usort($short_urls, function($a, $b) {
                    return $b->clicks - $a->clicks;
                });
                $top_urls = array_slice($short_urls, 0, 5);
                ?>

                <table class="wp-list-table widefat">
                    <thead>
                        <tr>
                            <th><?php _e('الرابط المختصر', 'ridcod-shorts'); ?></th>
                            <th><?php _e('عدد النقرات', 'ridcod-shorts'); ?></th>
                            <th><?php _e('النسبة المئوية', 'ridcod-shorts'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($top_urls as $url): ?>
                            <tr>
                                <td>
                                    <a href="<?php echo home_url('/' . $url->short_code); ?>" target="_blank">
                                        <?php echo esc_html($url->short_code); ?>
                                    </a>
                                </td>
                                <td><strong><?php echo number_format($url->clicks); ?></strong></td>
                                <td>
                                    <?php
                                    $percentage = $total_clicks > 0 ? round(($url->clicks / $total_clicks) * 100, 1) : 0;
                                    echo $percentage . '%';
                                    ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif;

        $html = ob_get_clean();

        wp_send_json_success(array(
            'html' => $html,
            'total_urls' => $total_urls,
            'total_clicks' => $total_clicks,
            'avg_clicks' => $avg_clicks
        ));
    }

    /**
     * إجبار تحديث قواعد الروابط عبر AJAX
     */
    public function force_rewrite_rules_update() {
        check_ajax_referer('ridcod_shorts_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('غير مسموح');
        }

        $this->add_rewrite_rules();
        flush_rewrite_rules();
        update_option('ridcod_shorts_rules_version', '3.0.0');
        update_option('ridcod_shorts_rules_updated', true);

        wp_send_json_success('تم تحديث قواعد الروابط بنجاح');
    }
    
    /**
     * توليد رمز عشوائي
     */
    private function generate_random_code($length = 6) {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $code = '';
        
        for ($i = 0; $i < $length; $i++) {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $code;
    }
    
    /**
     * ترقية قاعدة البيانات إذا لزم الأمر
     */
    private function upgrade_database() {
        $current_version = get_option('ridcod_shorts_db_version', '1.0.0');
        
        if (version_compare($current_version, '1.1.0', '<')) {
            global $wpdb;
            $table_name = $wpdb->prefix . RIDCOD_SHORTS_TABLE_NAME;
            
            // تغيير نوع العمود لدعم القيم النصية
            $wpdb->query("ALTER TABLE $table_name MODIFY intermediate_page_id varchar(255) DEFAULT NULL");
            
            update_option('ridcod_shorts_db_version', '1.1.0');
        }
    }
    
    /**
     * فحص وتحديث قواعد الروابط
     */
    private function check_rewrite_rules() {
        $current_rules_version = get_option('ridcod_shorts_rules_version', '1.0.0');

        if (version_compare($current_rules_version, '3.0.0', '<')) {
            $this->add_rewrite_rules();
            flush_rewrite_rules();
            update_option('ridcod_shorts_rules_version', '3.0.0');
            // إزالة العلامة القديمة لإجبار التحديث
            delete_option('ridcod_shorts_rules_updated');
        }
    }

    /**
     * فحص قواعد الروابط عند تحميل WordPress
     */
    public function check_rewrite_rules_on_load() {
        $rewrite_rules = get_option('rewrite_rules');
        $ridcod_rules_exist = false;

        if ($rewrite_rules) {
            foreach ($rewrite_rules as $pattern => $replacement) {
                if (strpos($replacement, 'ridcod_short') !== false) {
                    $ridcod_rules_exist = true;
                    break;
                }
            }
        }

        if (!$ridcod_rules_exist) {
            $this->add_rewrite_rules();
            flush_rewrite_rules();
            update_option('ridcod_shorts_rules_updated', true);
        }
    }

    /**
     * التحقق من عمل قواعد الروابط
     */
    public function test_rewrite_rules() {
        // اختبار بسيط لمعرفة إذا كانت قواعد الروابط تعمل
        $test_code = 'test123';
        $test_url = home_url('/' . $test_code);

        // إذا كان هناك رابط اختبار، نعرض معلومات مفيدة
        if (isset($_GET['ridcod_test'])) {
            echo '<div style="background: #f0f0f0; padding: 20px; margin: 20px; border-radius: 5px;">';
            echo '<h3>معلومات تشخيص إضافة اختصار الروابط</h3>';
            echo '<p><strong>رابط الاختبار:</strong> ' . $test_url . '</p>';
            echo '<p><strong>Query Var:</strong> ' . get_query_var('ridcod_short') . '</p>';
            echo '<p><strong>قواعد الروابط المسجلة:</strong></p>';
            echo '<pre>' . print_r(get_option('rewrite_rules'), true) . '</pre>';
            echo '</div>';
            exit;
        }
    }
}

// تهيئة الإضافة
new RidcodShorts();

// تحميل ملف الاختبار (في بيئة التطوير فقط)
if (defined('WP_DEBUG') && WP_DEBUG && current_user_can('manage_options')) {
    require_once RIDCOD_SHORTS_PLUGIN_DIR . 'test-product-222.php';
}
