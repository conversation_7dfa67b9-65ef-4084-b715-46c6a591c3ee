<?php
/**
 * Elementor Activate Widget for RIDCODTOKEN.
 *
 * @package RIDCODTOKEN
 */

namespace RIDCODTOKEN\Elementor\Widgets;

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

use Elementor\Widget_Base;
use Elementor\Controls_Manager;

/**
 * Elementor 'Activate License' Widget.
 */
class Activate_Widget extends Widget_Base {

	/**
	 * Get widget name.
	 */
	public function get_name() {
		return 'ridcodtoken-activate';
	}

	/**
	 * Get widget title.
	 */
	public function get_title() {
		return __( 'RIDCODTOKEN License Activation', 'ridcodtoken' );
	}

	/**
	 * Get widget icon.
	 */
	public function get_icon() {
		return 'eicon-lock-user'; // Or choose another relevant icon
	}

	/**
	 * Get widget categories.
	 */
	public function get_categories() {
		// return [ 'ridcodtoken' ]; // Use this if you registered a custom category
        return [ 'general' ]; // Or add to a standard category
	}

    /**
	 * Get widget keywords.
	 */
	public function get_keywords() {
		return [ 'ridcodtoken', 'license', 'activation', 'key', 'token' ];
	}

	/**
	 * Register widget controls.
	 */
	protected function _register_controls() {

		$this->start_controls_section(
			'content_section',
			[
				'label' => __( 'Content', 'ridcodtoken' ),
				'tab' => Controls_Manager::TAB_CONTENT,
			]
		);

        $this->add_control(
			'button_text',
			[
				'label' => __( 'Button Text', 'ridcodtoken' ),
				'type' => Controls_Manager::TEXT,
				'default' => __( 'Activate License', 'ridcodtoken' ),
				'placeholder' => __( 'Enter button text', 'ridcodtoken' ),
			]
		);

        $this->add_control(
			'input_placeholder',
			[
				'label' => __( 'Input Placeholder', 'ridcodtoken' ),
				'type' => Controls_Manager::TEXT,
				'default' => __( 'Enter your license key', 'ridcodtoken' ),
				'placeholder' => __( 'Enter placeholder text', 'ridcodtoken' ),
			]
		);

		$this->end_controls_section();

        // Add styling controls later if needed (e.g., button color, input style)
        $this->start_controls_section(
			'style_section',
			[
				'label' => __( 'Style', 'ridcodtoken' ),
				'tab' => Controls_Manager::TAB_STYLE,
			]
		);

        // Example: Button Typography
        $this->add_group_control(
			\Elementor\Group_Control_Typography::get_type(),
			[
				'name' => 'button_typography',
				'label' => __( 'Button Typography', 'ridcodtoken' ),
				'selector' => '{{WRAPPER}} .ridcodtoken-activate-button',
			]
		);

        // Example: Button Color
        $this->add_control(
			'button_color',
			[
				'label' => __( 'Button Color', 'ridcodtoken' ),
				'type' => Controls_Manager::COLOR,
				'selectors' => [
					'{{WRAPPER}} .ridcodtoken-activate-button' => 'background-color: {{VALUE}}; color: #fff;', // Basic example
				],
			]
		);


		$this->end_controls_section();

	}

	/**
	 * Render widget output on the frontend.
	 */
	protected function render() {
		$settings = $this->get_settings_for_display();
        $widget_id = $this->get_id(); // Unique ID for this widget instance

        // Check if already activated
        $activated_key = get_option('ridcodtoken_activated_key', '');
        $license_status = get_option('ridcodtoken_license_status', 'inactive');

        if (!empty($activated_key) && $license_status === 'active') {
            ?>
            <div class="ridcodtoken-activation-widget ridcodtoken-already-active">
                <p><?php esc_html_e('License is already active.', 'ridcodtoken'); ?> (<?php echo esc_html(substr($activated_key, 0, 4) . '...' . substr($activated_key, -4)); ?>)</p>
                <?php /* Optional: Add a deactivate button here if needed */ ?>
            </div>
            <?php
            return; // Don't show the form if already active
        }

		?>
		<div class="ridcodtoken-activation-widget" id="ridcodtoken-widget-<?php echo esc_attr($widget_id); ?>">
			<div class="ridcodtoken-form-container">
				<input type="text"
                       class="ridcodtoken-license-key-input"
                       placeholder="<?php echo esc_attr( $settings['input_placeholder'] ); ?>"
                       aria-label="<?php esc_attr_e( 'License Key', 'ridcodtoken' ); ?>">
				<button type="button" class="ridcodtoken-activate-button">
					<?php echo esc_html( $settings['button_text'] ); ?>
				</button>
			</div>
			<div class="ridcodtoken-message-area" style="margin-top: 10px; display: none;" aria-live="polite">
                <span class="ridcodtoken-spinner" style="display: none; vertical-align: middle; margin-right: 5px;">&#8987;</span> <!-- Basic spinner -->
                <span class="ridcodtoken-message-text"></span>
            </div>

            <?php // Add nonce field for AJAX request to save key ?>
            <?php wp_nonce_field( 'ridcodtoken_save_key_nonce', '_ridcodtoken_save_nonce' ); ?>
		</div>
		<?php
	}

    /**
	 * Get script dependencies. Enqueue frontend script.
	 */
	public function get_script_depends() {
		// Register the script first
        wp_register_script(
            'ridcodtoken-elementor-activate',
            RIDCODTOKEN_PLUGIN_URL . 'assets/js/ridcodtoken-elementor-activate.js',
            [ 'jquery', 'elementor-frontend' ], // Dependencies
            RIDCODTOKEN_VERSION, // Version
            true // Load in footer
        );

        // Localize script to pass data like AJAX URL, nonces, site URL
        wp_localize_script(
            'ridcodtoken-elementor-activate',
            'ridcodtoken_activate_params',
            [
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'rest_url' => esc_url_raw( rest_url( 'ridcodtoken/v1/activate' ) ),
                'site_url' => esc_url_raw( home_url() ),
                'nonce'    => wp_create_nonce( 'wp_rest' ), // Nonce for REST API
                'save_nonce_action' => 'ridcodtoken_save_key_nonce', // For matching the nonce field name in render()
                'save_action' => 'ridcodtoken_save_license', // AJAX action name for saving
                'text_activating' => __( 'Activating...', 'ridcodtoken' ),
                'text_error_general' => __( 'An error occurred. Please try again.', 'ridcodtoken' ),
                'text_error_invalid_key' => __( 'Please enter a valid license key.', 'ridcodtoken' ),
            ]
        );

		return [ 'ridcodtoken-elementor-activate' ]; // Return handle to enqueue
	}

    /**
     * Optional: Get style dependencies if you have a separate CSS file.
     */
    // public function get_style_depends() {
    //     wp_register_style( 'ridcodtoken-elementor-activate-style', RIDCODTOKEN_PLUGIN_URL . 'assets/css/ridcodtoken-elementor-activate.css', [], RIDCODTOKEN_VERSION );
    //     return [ 'ridcodtoken-elementor-activate-style' ];
    // }

}