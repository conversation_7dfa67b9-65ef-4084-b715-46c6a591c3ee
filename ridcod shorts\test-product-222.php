<?php
/**
 * اختبار سريع للتأكد من عمل Ridcod Shorts مع المنتج 222
 * 
 * هذا الملف للاختبار فقط - احذفه بعد التأكد من عمل النظام
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit('Direct access not allowed');
}

/**
 * اختبار ربط الإضافة بالمنتج 222
 */
function ridcod_shorts_test_product_222() {
    
    echo "<div style='background: #f0f8ff; padding: 20px; margin: 20px; border: 2px solid #007cba; border-radius: 8px;'>";
    echo "<h2>🧪 اختبار Ridcod Shorts - المنتج 222</h2>";
    
    // 1. اختبار إعدادات SDK
    echo "<h3>1️⃣ اختبار إعدادات SDK</h3>";
    
    global $ridcod_shorts_license_manager;
    
    if ($ridcod_shorts_license_manager) {
        $license_info = $ridcod_shorts_license_manager->get_license_info();
        
        echo "<table style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f9f9f9;'>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'><strong>حالة SDK:</strong></td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd; color: green;'>✅ محمل بنجاح</td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'><strong>معرف المنتج:</strong></td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'><code>222</code> ✅</td>";
        echo "</tr>";
        
        echo "<tr style='background: #f9f9f9;'>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'><strong>حالة الترخيص:</strong></td>";
        if ($license_info['is_valid']) {
            echo "<td style='padding: 8px; border: 1px solid #ddd; color: green;'>✅ مفعل</td>";
        } else {
            echo "<td style='padding: 8px; border: 1px solid #ddd; color: red;'>❌ غير مفعل</td>";
        }
        echo "</tr>";
        
        echo "<tr>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'><strong>مفتاح الترخيص:</strong></td>";
        if (!empty($license_info['license_key'])) {
            $masked_key = substr($license_info['license_key'], 0, 8) . '****' . substr($license_info['license_key'], -8);
            echo "<td style='padding: 8px; border: 1px solid #ddd;'><code>$masked_key</code></td>";
        } else {
            echo "<td style='padding: 8px; border: 1px solid #ddd; color: red;'>غير موجود</td>";
        }
        echo "</tr>";
        
        echo "</table>";
        
    } else {
        echo "<p style='color: red;'>❌ SDK غير محمل</p>";
    }
    
    // 2. اختبار قاعدة البيانات
    echo "<h3>2️⃣ اختبار قاعدة البيانات</h3>";
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'ridcod_shorts';
    
    // التحقق من وجود الجدول
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
    
    if ($table_exists) {
        echo "<p style='color: green;'>✅ جدول قاعدة البيانات موجود: <code>$table_name</code></p>";
        
        // عدد الروابط المختصرة
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        echo "<p>📊 عدد الروابط المختصرة: <strong>$count</strong></p>";
        
    } else {
        echo "<p style='color: red;'>❌ جدول قاعدة البيانات غير موجود</p>";
    }
    
    // 3. اختبار الوظائف
    echo "<h3>3️⃣ اختبار الوظائف</h3>";
    
    // التحقق من تحميل الكلاس الرئيسي
    if (class_exists('RidcodShorts')) {
        echo "<p style='color: green;'>✅ الكلاس الرئيسي محمل: <code>RidcodShorts</code></p>";
    } else {
        echo "<p style='color: red;'>❌ الكلاس الرئيسي غير محمل</p>";
    }
    
    // التحقق من الثوابت
    if (defined('RIDCOD_SHORTS_VERSION')) {
        echo "<p style='color: green;'>✅ إصدار الإضافة: <code>" . RIDCOD_SHORTS_VERSION . "</code></p>";
    } else {
        echo "<p style='color: red;'>❌ إصدار الإضافة غير محدد</p>";
    }
    
    // 4. اختبار API
    echo "<h3>4️⃣ اختبار الاتصال بـ API</h3>";
    
    $api_url = 'https://ridcod.com/wp-json/ridcodtoken/v1/status';
    $test_params = array(
        'license_key' => 'test-key-for-product-222',
        'site_url' => home_url(),
        'product_id' => 222
    );
    
    echo "<p><strong>رابط API:</strong> <code>$api_url</code></p>";
    echo "<p><strong>معاملات الاختبار:</strong></p>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 4px;'>";
    echo json_encode($test_params, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    echo "</pre>";
    
    // محاولة الاتصال (اختبار بسيط)
    $response = wp_remote_get($api_url . '?' . http_build_query($test_params), array(
        'timeout' => 10,
        'headers' => array(
            'User-Agent' => 'RIDCODTOKEN-SDK-TEST/1.0.0'
        )
    ));
    
    if (is_wp_error($response)) {
        echo "<p style='color: orange;'>⚠️ لا يمكن الوصول لـ API: " . $response->get_error_message() . "</p>";
    } else {
        $status_code = wp_remote_retrieve_response_code($response);
        echo "<p style='color: green;'>✅ API يستجيب - كود الحالة: <code>$status_code</code></p>";
    }
    
    // 5. تعليمات الاختبار
    echo "<h3>5️⃣ خطوات الاختبار اليدوي</h3>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 4px;'>";
    echo "<h4>🔍 للتأكد من عمل النظام بشكل كامل:</h4>";
    echo "<ol>";
    echo "<li><strong>أنشئ ترخيص جديد:</strong> اذهب إلى License Manager > Add New وضع Product ID = 222</li>";
    echo "<li><strong>فعل الترخيص:</strong> اذهب إلى Ridcod Shorts وأدخل الترخيص</li>";
    echo "<li><strong>اختبر الوظائف:</strong> أنشئ رابط مختصر جديد</li>";
    echo "<li><strong>اختبر الحماية:</strong> جرب ترخيص منتج آخر (يجب أن يفشل)</li>";
    echo "</ol>";
    echo "</div>";
    
    // 6. روابط مفيدة
    echo "<h3>6️⃣ روابط مفيدة</h3>";
    
    echo "<p>";
    echo "<a href='" . admin_url('admin.php?page=ridcodtoken-license-manager') . "' class='button button-primary'>إدارة التراخيص</a> ";
    echo "<a href='" . admin_url('admin.php?page=ridcodtoken-add-license') . "' class='button button-secondary'>إضافة ترخيص جديد</a> ";
    echo "<a href='" . admin_url('admin.php?page=ridcod-shorts') . "' class='button button-secondary'>Ridcod Shorts</a>";
    echo "</p>";
    
    echo "<hr>";
    echo "<p style='text-align: center; color: #666;'>";
    echo "🔒 <strong>النظام الجديد:</strong> كل ترخيص مرتبط بمنتج محدد لضمان الأمان<br>";
    echo "📅 <strong>تاريخ التحديث:</strong> " . date('Y-m-d H:i:s') . "<br>";
    echo "🆔 <strong>معرف المنتج:</strong> 222";
    echo "</p>";
    
    echo "</div>";
}

// عرض الاختبار إذا تم طلبه
if (isset($_GET['ridcod_test_222']) && current_user_can('manage_options')) {
    add_action('admin_notices', 'ridcod_shorts_test_product_222');
}

// إضافة رابط الاختبار في قائمة الإدارة
add_action('admin_menu', function() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            'ridcod-shorts',
            'اختبار المنتج 222',
            '🧪 اختبار النظام',
            'manage_options',
            'ridcod-shorts-test',
            function() {
                echo '<div class="wrap">';
                ridcod_shorts_test_product_222();
                echo '</div>';
            }
        );
    }
});

/**
 * دالة مساعدة لاختبار ترخيص معين
 */
function ridcod_shorts_test_specific_license($license_key) {
    
    if (empty($license_key)) {
        return array('success' => false, 'message' => 'مفتاح الترخيص مطلوب');
    }
    
    $api_url = 'https://ridcod.com/wp-json/ridcodtoken/v1/status';
    
    $response = wp_remote_get($api_url . '?' . http_build_query(array(
        'license_key' => $license_key,
        'site_url' => home_url(),
        'product_id' => 222
    )), array(
        'timeout' => 30,
        'headers' => array(
            'User-Agent' => 'RIDCODTOKEN-SDK-TEST/' . RIDCOD_SHORTS_VERSION
        )
    ));
    
    if (is_wp_error($response)) {
        return array(
            'success' => false, 
            'message' => 'فشل الاتصال: ' . $response->get_error_message()
        );
    }
    
    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);
    
    if (!$data) {
        return array('success' => false, 'message' => 'استجابة غير صحيحة من الخادم');
    }
    
    return array(
        'success' => true,
        'data' => $data,
        'is_valid' => (
            isset($data['status']) && $data['status'] === 'active' &&
            isset($data['site_is_active']) && $data['site_is_active'] === true &&
            (!isset($data['is_expired']) || $data['is_expired'] !== true)
        )
    );
}

// إضافة معالج AJAX لاختبار الترخيص
add_action('wp_ajax_ridcod_test_license_222', function() {
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error('ليس لديك صلاحية');
    }
    
    $license_key = sanitize_text_field($_POST['license_key'] ?? '');
    $result = ridcod_shorts_test_specific_license($license_key);
    
    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result);
    }
});
?>
