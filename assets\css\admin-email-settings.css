/* RIDCODTOKEN Email Settings Admin Styles */

.ridcodtoken-email-settings {
    max-width: 1200px;
}

.email-setting-section {
    background: #fff;
    border: 1px solid #c3c4c7;
    margin: 20px 0;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.email-setting-section h2 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #1d2327;
    font-size: 1.3em;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.email-setting-section .description {
    margin-bottom: 20px;
    color: #646970;
    font-style: italic;
}

.email-setting-section .form-table th {
    width: 200px;
    font-weight: 600;
    color: #1d2327;
}

.email-setting-section .form-table td {
    padding-bottom: 20px;
}

.email-setting-section .form-table .description {
    margin-top: 5px;
    margin-bottom: 0;
    font-size: 13px;
    color: #646970;
}

.email-setting-section .form-table .description code {
    background: #f0f0f1;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 12px;
    color: #d63384;
}

/* معاينة الرسائل */
.email-preview-section {
    background: #fff;
    border: 1px solid #c3c4c7;
    margin: 20px 0;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.email-preview-section h2 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #1d2327;
    font-size: 1.3em;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.email-preview-tabs {
    margin-bottom: 15px;
}

.preview-tab {
    margin-right: 10px;
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    color: #50575e;
}

.preview-tab.active {
    background: #0073aa;
    color: #fff;
    border-color: #0073aa;
}

.email-preview-content {
    display: none;
}

.email-preview-content.active {
    display: block;
}

.email-preview-box {
    background: #f9f9f9;
    border: 1px solid #ddd;
    padding: 15px;
    border-radius: 4px;
    margin-top: 10px;
}

.email-preview-box hr {
    margin: 10px 0;
    border: 0;
    border-top: 1px solid #ddd;
}

.preview-subject {
    font-weight: bold;
    color: #1d2327;
}

.preview-content {
    line-height: 1.6;
    color: #3c434a;
}

.preview-content code {
    background: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* تحسينات للنماذج */
.ridcodtoken-email-settings .wp-editor-wrap {
    border: 1px solid #c3c4c7;
    border-radius: 4px;
}

.ridcodtoken-email-settings input[type="text"],
.ridcodtoken-email-settings input[type="number"] {
    padding: 8px 12px;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
}

.ridcodtoken-email-settings input[type="text"]:focus,
.ridcodtoken-email-settings input[type="number"]:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

.ridcodtoken-email-settings input[type="checkbox"] {
    margin-right: 8px;
}

/* تحسينات للأزرار */
.ridcodtoken-email-settings .submit {
    padding-left: 0;
}

.ridcodtoken-email-settings #submit {
    background: #2271b1;
    border-color: #2271b1;
    color: #fff;
    padding: 10px 20px;
    font-size: 14px;
    border-radius: 4px;
    box-shadow: 0 1px 0 #135e96;
}

.ridcodtoken-email-settings #submit:hover {
    background: #135e96;
    border-color: #135e96;
}

/* تنسيق متجاوب */
@media screen and (max-width: 782px) {
    .email-setting-section .form-table th,
    .email-setting-section .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .email-setting-section .form-table th {
        border-bottom: none;
    }
    
    .preview-tab {
        display: block;
        width: 100%;
        margin: 5px 0;
    }
}

/* إضافات للملصقات والأيقونات */
.email-setting-section h2:before {
    content: "\f466";
    font-family: "dashicons";
    margin-right: 8px;
    color: #0073aa;
}

.email-preview-section h2:before {
    content: "\f177";
    font-family: "dashicons";
    margin-right: 8px;
    color: #0073aa;
}

/* تحسينات لحالة التفعيل/إلغاء التفعيل */
.ridcodtoken-email-settings input[type="checkbox"]:checked + label {
    color: #135e96;
    font-weight: 500;
}

/* تنسيق الأكواد في المحتوى */
.email-setting-section code {
    background: #f0f0f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: Consolas, Monaco, monospace;
    font-size: 13px;
    color: #d63384;
    border: 1px solid #e1e1e1;
    cursor: pointer;
    transition: all 0.2s ease;
}

.email-setting-section code:hover {
    background: #e1e1e1;
    transform: translateY(-1px);
}

/* تحسينات لقسم الرسائل التجريبية */
.email-setting-section:last-of-type {
    border-left: 4px solid #2271b1;
}

.email-setting-section:last-of-type h2 {
    color: #2271b1;
}

/* تأثيرات تحميل للأزرار */
.button.loading {
    position: relative;
    pointer-events: none;
}

.button.loading:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -8px 0 0 -8px;
    width: 16px;
    height: 16px;
    border: 2px solid #fff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
