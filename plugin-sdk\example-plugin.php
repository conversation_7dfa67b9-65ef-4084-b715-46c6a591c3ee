<?php
/**
 * Plugin Name: مثال على إضافة محمية
 * Plugin URI: https://example.com
 * Description: مثال عملي على كيفية استخدام RIDCODTOKEN Plugin SDK لحماية الإضافات
 * Version: 1.0.0
 * Author: اسم المطور
 * License: GPL v2 or later
 * Text Domain: example-protected-plugin
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// تحميل SDK الحماية
require_once plugin_dir_path(__FILE__) . 'ridcodtoken-plugin-sdk.php';

// إعداد SDK
$license_sdk = ridcodtoken_init_plugin_sdk(array(
    'plugin_file' => __FILE__,                           // مسار الملف الرئيسي للإضافة
    'plugin_name' => 'مثال على إضافة محمية',              // اسم الإضافة للعرض
    'api_url' => 'https://yoursite.com',                 // رابط موقع RIDCODTOKEN
    'plugin_slug' => 'example-protected-plugin',         // معرف فريد للإضافة
    'product_id' => 123,                                 // معرف المنتج في WooCommerce (مطلوب)
    'version' => '1.0.0'                                 // إصدار الإضافة
));

// التحقق من صحة الترخيص قبل تحميل وظائف الإضافة
if (!$license_sdk->is_licensed()) {
    // إذا لم يكن الترخيص صحيح، لا تحمل باقي الإضافة
    return;
}

/**
 * الآن يمكنك وضع كود الإضافة هنا
 * سيتم تنفيذ هذا الجزء فقط إذا كان الترخيص صحيح
 */

// تعريف الثوابت
define('EXAMPLE_PLUGIN_VERSION', '1.0.0');
define('EXAMPLE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('EXAMPLE_PLUGIN_PATH', plugin_dir_path(__FILE__));

/**
 * الفئة الرئيسية للإضافة
 */
class Example_Protected_Plugin {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // إضافة shortcode
        add_shortcode('example_shortcode', array($this, 'example_shortcode'));
        
        // إضافة widget
        add_action('widgets_init', array($this, 'register_widgets'));
    }
    
    /**
     * تهيئة الإضافة
     */
    public function init() {
        // تحميل ملفات الترجمة
        load_plugin_textdomain('example-protected-plugin', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // إنشاء جداول قاعدة البيانات إذا لزم الأمر
        $this->create_tables();
        
        // تسجيل post types مخصصة
        $this->register_post_types();
    }
    
    /**
     * إضافة قائمة في لوحة الإدارة
     */
    public function add_admin_menu() {
        add_menu_page(
            'الإضافة المحمية',
            'الإضافة المحمية',
            'manage_options',
            'example-protected-plugin',
            array($this, 'admin_page'),
            'dashicons-shield',
            30
        );
        
        add_submenu_page(
            'example-protected-plugin',
            'الإعدادات',
            'الإعدادات',
            'manage_options',
            'example-protected-plugin-settings',
            array($this, 'settings_page')
        );
    }
    
    /**
     * صفحة الإدارة الرئيسية
     */
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>الإضافة المحمية</h1>
            <div class="notice notice-success">
                <p><strong>تهانينا!</strong> الإضافة تعمل بشكل صحيح مع نظام الحماية RIDCODTOKEN.</p>
            </div>
            
            <div class="card">
                <h2>معلومات الإضافة</h2>
                <p><strong>الاسم:</strong> مثال على إضافة محمية</p>
                <p><strong>الإصدار:</strong> <?php echo EXAMPLE_PLUGIN_VERSION; ?></p>
                <p><strong>حالة الترخيص:</strong> <span style="color: green;">✓ مفعل</span></p>
                <p><strong>آخر تحقق:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
            </div>
            
            <div class="card">
                <h2>الوظائف المتاحة</h2>
                <ul>
                    <li>✓ قائمة إدارة مخصصة</li>
                    <li>✓ صفحة إعدادات</li>
                    <li>✓ Shortcode: [example_shortcode]</li>
                    <li>✓ Widget مخصص</li>
                    <li>✓ Post Type مخصص</li>
                    <li>✓ جداول قاعدة بيانات</li>
                </ul>
            </div>
            
            <div class="card">
                <h2>اختبار الـ Shortcode</h2>
                <p>استخدم الكود التالي في أي صفحة أو مقال:</p>
                <code>[example_shortcode title="مرحبا" content="هذا محتوى من الإضافة المحمية"]</code>
            </div>
        </div>
        <?php
    }
    
    /**
     * صفحة الإعدادات
     */
    public function settings_page() {
        // حفظ الإعدادات
        if (isset($_POST['save_settings'])) {
            update_option('example_plugin_setting1', sanitize_text_field($_POST['setting1']));
            update_option('example_plugin_setting2', sanitize_textarea_field($_POST['setting2']));
            echo '<div class="notice notice-success"><p>تم حفظ الإعدادات بنجاح!</p></div>';
        }
        
        $setting1 = get_option('example_plugin_setting1', '');
        $setting2 = get_option('example_plugin_setting2', '');
        ?>
        <div class="wrap">
            <h1>إعدادات الإضافة المحمية</h1>
            
            <form method="post" action="">
                <table class="form-table">
                    <tr>
                        <th scope="row">الإعداد الأول</th>
                        <td>
                            <input type="text" name="setting1" value="<?php echo esc_attr($setting1); ?>" class="regular-text" />
                            <p class="description">وصف للإعداد الأول</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">الإعداد الثاني</th>
                        <td>
                            <textarea name="setting2" rows="5" cols="50"><?php echo esc_textarea($setting2); ?></textarea>
                            <p class="description">وصف للإعداد الثاني</p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button('حفظ الإعدادات', 'primary', 'save_settings'); ?>
            </form>
        </div>
        <?php
    }
    
    /**
     * تحميل ملفات CSS و JS للواجهة الأمامية
     */
    public function enqueue_scripts() {
        wp_enqueue_style('example-plugin-style', EXAMPLE_PLUGIN_URL . 'assets/style.css', array(), EXAMPLE_PLUGIN_VERSION);
        wp_enqueue_script('example-plugin-script', EXAMPLE_PLUGIN_URL . 'assets/script.js', array('jquery'), EXAMPLE_PLUGIN_VERSION, true);
    }
    
    /**
     * تحميل ملفات CSS و JS للوحة الإدارة
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'example-protected-plugin') !== false) {
            wp_enqueue_style('example-plugin-admin-style', EXAMPLE_PLUGIN_URL . 'assets/admin-style.css', array(), EXAMPLE_PLUGIN_VERSION);
            wp_enqueue_script('example-plugin-admin-script', EXAMPLE_PLUGIN_URL . 'assets/admin-script.js', array('jquery'), EXAMPLE_PLUGIN_VERSION, true);
        }
    }
    
    /**
     * Shortcode مثال
     */
    public function example_shortcode($atts) {
        $atts = shortcode_atts(array(
            'title' => 'العنوان الافتراضي',
            'content' => 'المحتوى الافتراضي'
        ), $atts);
        
        $output = '<div class="example-shortcode">';
        $output .= '<h3>' . esc_html($atts['title']) . '</h3>';
        $output .= '<p>' . esc_html($atts['content']) . '</p>';
        $output .= '<p><small>تم إنشاؤه بواسطة الإضافة المحمية</small></p>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * تسجيل Widget
     */
    public function register_widgets() {
        register_widget('Example_Protected_Widget');
    }
    
    /**
     * إنشاء جداول قاعدة البيانات
     */
    private function create_tables() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'example_plugin_data';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            content text NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * تسجيل Post Types مخصصة
     */
    private function register_post_types() {
        register_post_type('example_item', array(
            'labels' => array(
                'name' => 'العناصر المحمية',
                'singular_name' => 'عنصر محمي',
                'add_new' => 'إضافة جديد',
                'add_new_item' => 'إضافة عنصر جديد',
                'edit_item' => 'تعديل العنصر',
                'new_item' => 'عنصر جديد',
                'view_item' => 'عرض العنصر',
                'search_items' => 'البحث في العناصر',
                'not_found' => 'لم يتم العثور على عناصر',
                'not_found_in_trash' => 'لم يتم العثور على عناصر في المهملات'
            ),
            'public' => true,
            'has_archive' => true,
            'supports' => array('title', 'editor', 'thumbnail'),
            'menu_icon' => 'dashicons-shield-alt'
        ));
    }
}

/**
 * Widget مخصص
 */
class Example_Protected_Widget extends WP_Widget {
    
    public function __construct() {
        parent::__construct(
            'example_protected_widget',
            'Widget محمي',
            array('description' => 'Widget من الإضافة المحمية')
        );
    }
    
    public function widget($args, $instance) {
        echo $args['before_widget'];
        
        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }
        
        echo '<p>' . esc_html($instance['content']) . '</p>';
        echo '<p><small>من الإضافة المحمية</small></p>';
        
        echo $args['after_widget'];
    }
    
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : 'العنوان';
        $content = !empty($instance['content']) ? $instance['content'] : 'المحتوى';
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>">العنوان:</label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('content'); ?>">المحتوى:</label>
            <textarea class="widefat" id="<?php echo $this->get_field_id('content'); ?>" name="<?php echo $this->get_field_name('content'); ?>"><?php echo esc_attr($content); ?></textarea>
        </p>
        <?php
    }
    
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? strip_tags($new_instance['title']) : '';
        $instance['content'] = (!empty($new_instance['content'])) ? strip_tags($new_instance['content']) : '';
        return $instance;
    }
}

// تشغيل الإضافة
new Example_Protected_Plugin();

// Hook للتفعيل
register_activation_hook(__FILE__, 'example_plugin_activate');
function example_plugin_activate() {
    // كود التفعيل هنا
    flush_rewrite_rules();
}

// Hook لإلغاء التفعيل
register_deactivation_hook(__FILE__, 'example_plugin_deactivate');
function example_plugin_deactivate() {
    // كود إلغاء التفعيل هنا
    flush_rewrite_rules();
}

/**
 * دالة مساعدة للحصول على معلومات الترخيص
 */
function example_plugin_get_license_info() {
    global $license_sdk;
    if ($license_sdk) {
        return $license_sdk->get_license_info();
    }
    return false;
}

/**
 * دالة مساعدة للتحقق من صحة الترخيص
 */
function example_plugin_is_licensed() {
    global $license_sdk;
    if ($license_sdk) {
        return $license_sdk->is_licensed();
    }
    return false;
}
?>
