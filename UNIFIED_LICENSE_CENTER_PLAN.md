# مركز التراخيص الموحد - RIDCODTOKEN License Center

## 🎯 الهدف
إنشاء صفحة واحدة موحدة لإدارة جميع تراخيص إضافات RIDCODTOKEN بدلاً من صفحة منفصلة لكل إضافة.

---

## 🔍 المشكلة الحالية

### عند شراء إضافات متعددة:
- ✅ إضافة A → صفحة تفعيل منفصلة
- ✅ إضافة B → صفحة تفعيل منفصلة  
- ✅ إضافة C → صفحة تفعيل منفصلة
- ✅ إضافة D → صفحة تفعيل منفصلة

**النتيجة:** 4 صفحات منفصلة = تجربة مستخدم سيئة! 😫

---

## 💡 الحل المقترح

### مركز التراخيص الموحد:
```
📋 RIDCODTOKEN License Center
├── 🔑 My Licenses (جميع التراخيص)
├── ✅ Active Plugins (الإضافات المفعلة)
├── ❌ Inactive Plugins (الإضافات غير المفعلة)
├── ⚙️ Settings (الإعدادات العامة)
└── 📊 Statistics (إحصائيات الاستخدام)
```

---

## 🏗️ التصميم المقترح

### 1. الصفحة الرئيسية
```php
// مثال على التخطيط
┌─────────────────────────────────────────────────────────┐
│ 🏠 RIDCODTOKEN License Center                           │
├─────────────────────────────────────────────────────────┤
│ 📊 Overview                                             │
│ • Total Plugins: 4                                     │
│ • Active: 3 | Inactive: 1                              │
│ • Last Check: 2 hours ago                              │
├─────────────────────────────────────────────────────────┤
│ 🔑 Quick License Activation                             │
│ [License Key Input] [Activate Button]                  │
├─────────────────────────────────────────────────────────┤
│ 📋 Installed Plugins                                    │
│                                                         │
│ ✅ Ridcod Shorts (Product #222)                        │
│    Status: Active | Expires: Never                     │
│    [Manage] [Deactivate]                               │
│                                                         │
│ ✅ Plugin B (Product #333)                             │
│    Status: Active | Expires: 2024-12-31                │
│    [Manage] [Deactivate]                               │
│                                                         │
│ ❌ Plugin C (Product #444)                             │
│    Status: Inactive | Needs License                    │
│    [Activate License]                                   │
└─────────────────────────────────────────────────────────┘
```

### 2. نظام الكشف التلقائي
```php
// الكشف عن جميع إضافات RIDCODTOKEN المثبتة
function ridcodtoken_detect_installed_plugins() {
    $plugins = array();
    
    // البحث في جميع الإضافات المثبتة
    foreach (get_plugins() as $plugin_file => $plugin_data) {
        $plugin_dir = dirname(WP_PLUGIN_DIR . '/' . $plugin_file);
        
        // البحث عن ملف SDK
        if (file_exists($plugin_dir . '/sdk/ridcodtoken-plugin-sdk.php') ||
            file_exists($plugin_dir . '/ridcodtoken-plugin-sdk.php')) {
            
            $plugins[] = array(
                'file' => $plugin_file,
                'name' => $plugin_data['Name'],
                'version' => $plugin_data['Version'],
                'status' => is_plugin_active($plugin_file) ? 'active' : 'inactive',
                'license_status' => ridcodtoken_get_plugin_license_status($plugin_file)
            );
        }
    }
    
    return $plugins;
}
```

### 3. إدارة التراخيص الموحدة
```php
// تفعيل ترخيص لإضافة محددة
function ridcodtoken_activate_license_for_plugin($license_key, $plugin_slug, $product_id) {
    // التحقق من صحة الترخيص للمنتج المحدد
    $api_response = ridcodtoken_verify_license($license_key, $product_id);
    
    if ($api_response['success']) {
        // حفظ الترخيص للإضافة المحددة
        update_option("ridcodtoken_{$plugin_slug}_license_key", $license_key);
        update_option("ridcodtoken_{$plugin_slug}_license_status", 'active');
        
        return array('success' => true, 'message' => 'تم تفعيل الترخيص بنجاح');
    }
    
    return $api_response;
}
```

---

## 🔧 التطبيق العملي

### المرحلة 1: إنشاء المركز الموحد

#### 1. ملف المركز الرئيسي
```php
// includes/license-center/unified-center.php
class RIDCODTOKEN_Unified_License_Center {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_unified_menu'));
        add_action('wp_ajax_ridcodtoken_unified_activate', array($this, 'ajax_activate_license'));
        add_action('wp_ajax_ridcodtoken_unified_deactivate', array($this, 'ajax_deactivate_license'));
    }
    
    public function add_unified_menu() {
        // إضافة قائمة رئيسية موحدة
        add_menu_page(
            'RIDCODTOKEN License Center',
            'License Center',
            'manage_options',
            'ridcodtoken-center',
            array($this, 'render_center_page'),
            'dashicons-admin-network',
            25
        );
    }
    
    public function render_center_page() {
        $installed_plugins = $this->detect_ridcodtoken_plugins();
        include RIDCODTOKEN_PLUGIN_DIR . 'includes/license-center/center-page.php';
    }
    
    private function detect_ridcodtoken_plugins() {
        // كشف جميع الإضافات التي تستخدم RIDCODTOKEN
        // ...
    }
}
```

#### 2. واجهة المستخدم
```html
<!-- includes/license-center/center-page.php -->
<div class="wrap ridcodtoken-license-center">
    <h1>🏠 RIDCODTOKEN License Center</h1>
    
    <!-- نظرة عامة -->
    <div class="ridcodtoken-overview">
        <div class="stats-grid">
            <div class="stat-box">
                <h3><?php echo count($installed_plugins); ?></h3>
                <p>Total Plugins</p>
            </div>
            <div class="stat-box active">
                <h3><?php echo count(array_filter($installed_plugins, function($p) { return $p['license_status'] === 'active'; })); ?></h3>
                <p>Active Licenses</p>
            </div>
            <div class="stat-box inactive">
                <h3><?php echo count(array_filter($installed_plugins, function($p) { return $p['license_status'] !== 'active'; })); ?></h3>
                <p>Need Activation</p>
            </div>
        </div>
    </div>
    
    <!-- تفعيل سريع -->
    <div class="ridcodtoken-quick-activation">
        <h2>🔑 Quick License Activation</h2>
        <form id="ridcodtoken-quick-form">
            <table class="form-table">
                <tr>
                    <th>License Key</th>
                    <td>
                        <input type="text" id="license-key" class="regular-text" placeholder="Enter your license key" />
                    </td>
                </tr>
                <tr>
                    <th>Plugin</th>
                    <td>
                        <select id="target-plugin">
                            <option value="">Auto-detect from license</option>
                            <?php foreach ($installed_plugins as $plugin): ?>
                                <option value="<?php echo $plugin['slug']; ?>" data-product="<?php echo $plugin['product_id']; ?>">
                                    <?php echo $plugin['name']; ?> (Product #<?php echo $plugin['product_id']; ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </td>
                </tr>
            </table>
            <p class="submit">
                <button type="submit" class="button button-primary">Activate License</button>
            </p>
        </form>
    </div>
    
    <!-- قائمة الإضافات -->
    <div class="ridcodtoken-plugins-list">
        <h2>📋 Installed RIDCODTOKEN Plugins</h2>
        
        <?php foreach ($installed_plugins as $plugin): ?>
            <div class="plugin-card <?php echo $plugin['license_status']; ?>">
                <div class="plugin-info">
                    <h3><?php echo $plugin['name']; ?></h3>
                    <p>Product ID: #<?php echo $plugin['product_id']; ?> | Version: <?php echo $plugin['version']; ?></p>
                </div>
                
                <div class="plugin-status">
                    <?php if ($plugin['license_status'] === 'active'): ?>
                        <span class="status-badge active">✅ Active</span>
                        <button class="button deactivate-btn" data-plugin="<?php echo $plugin['slug']; ?>">Deactivate</button>
                    <?php else: ?>
                        <span class="status-badge inactive">❌ Inactive</span>
                        <button class="button button-primary activate-btn" data-plugin="<?php echo $plugin['slug']; ?>" data-product="<?php echo $plugin['product_id']; ?>">Activate License</button>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>
```

### المرحلة 2: تحديث SDK للتكامل

#### تحديث SDK ليتعرف على المركز الموحد
```php
// في ridcodtoken-plugin-sdk.php
private function init() {
    // التحقق من وجود المركز الموحد
    if ($this->unified_center_exists()) {
        // عدم إضافة قائمة منفصلة
        $this->register_with_unified_center();
    } else {
        // إضافة القائمة المنفصلة كما هو معتاد
        if (!$this->is_license_valid()) {
            add_action('admin_menu', array($this, 'add_license_menu'));
        } else {
            add_action('admin_menu', array($this, 'add_license_management_menu'));
        }
    }
}

private function unified_center_exists() {
    return class_exists('RIDCODTOKEN_Unified_License_Center');
}

private function register_with_unified_center() {
    // تسجيل الإضافة مع المركز الموحد
    add_filter('ridcodtoken_unified_plugins', function($plugins) {
        $plugins[] = array(
            'slug' => $this->plugin_info['plugin_slug'],
            'name' => $this->plugin_info['plugin_name'],
            'product_id' => $this->plugin_info['product_id'],
            'version' => $this->plugin_info['version'],
            'license_status' => $this->license_status,
            'license_key' => $this->license_key
        );
        return $plugins;
    });
}
```

---

## 🎨 المميزات الإضافية

### 1. التفعيل التلقائي الذكي
```php
// عند إدخال ترخيص، النظام يكتشف المنتج تلقائياً
function ridcodtoken_smart_activation($license_key) {
    // استعلام API لمعرفة معرف المنتج
    $product_info = ridcodtoken_get_license_product_info($license_key);
    
    if ($product_info['success']) {
        $product_id = $product_info['product_id'];
        
        // البحث عن الإضافة المطابقة
        $target_plugin = ridcodtoken_find_plugin_by_product_id($product_id);
        
        if ($target_plugin) {
            return ridcodtoken_activate_license_for_plugin($license_key, $target_plugin['slug'], $product_id);
        }
    }
    
    return array('success' => false, 'message' => 'لم يتم العثور على إضافة مطابقة لهذا الترخيص');
}
```

### 2. إشعارات موحدة
```php
// إشعار واحد بدلاً من إشعارات متعددة
function ridcodtoken_unified_admin_notices() {
    $inactive_plugins = ridcodtoken_get_inactive_plugins();
    
    if (!empty($inactive_plugins)) {
        echo '<div class="notice notice-warning">';
        echo '<p><strong>RIDCODTOKEN:</strong> ';
        echo sprintf('لديك %d إضافة تحتاج لتفعيل الترخيص. ', count($inactive_plugins));
        echo '<a href="' . admin_url('admin.php?page=ridcodtoken-center') . '">إدارة التراخيص</a>';
        echo '</p>';
        echo '</div>';
    }
}
```

### 3. تحديث مجمع
```php
// تحديث جميع التراخيص دفعة واحدة
function ridcodtoken_bulk_license_check() {
    $plugins = ridcodtoken_get_all_plugins();
    $results = array();
    
    foreach ($plugins as $plugin) {
        if ($plugin['license_status'] === 'active') {
            $check_result = ridcodtoken_verify_license_with_server($plugin['license_key'], $plugin['product_id']);
            $results[$plugin['slug']] = $check_result;
        }
    }
    
    return $results;
}
```

---

## 📊 الفوائد المتوقعة

### للمستخدمين:
- ✅ **صفحة واحدة** لجميع التراخيص
- ✅ **تفعيل سريع** بنقرة واحدة
- ✅ **نظرة شاملة** على جميع الإضافات
- ✅ **إدارة مركزية** سهلة

### للمطورين:
- ✅ **تجربة مستخدم أفضل**
- ✅ **تقليل الشكاوى** حول تعقيد التفعيل
- ✅ **إحصائيات موحدة** لجميع المنتجات
- ✅ **صيانة أسهل** للنظام

---

## 🚀 خطة التنفيذ

### المرحلة 1 (3 أيام):
- [ ] إنشاء المركز الموحد الأساسي
- [ ] تحديث SDK للتكامل
- [ ] اختبار مع إضافتين

### المرحلة 2 (2 يوم):
- [ ] إضافة التفعيل الذكي
- [ ] تحسين واجهة المستخدم
- [ ] إضافة الإحصائيات

### المرحلة 3 (1 يوم):
- [ ] اختبار شامل
- [ ] توثيق النظام
- [ ] نشر التحديث

---

## ❓ هل تريد تطبيق هذا النظام؟

هذا سيحل مشكلة تعدد صفحات التفعيل نهائياً ويجعل تجربة المستخدم أفضل بكثير!

**ما رأيك؟ هل نبدأ في تطبيق مركز التراخيص الموحد؟** 🚀
