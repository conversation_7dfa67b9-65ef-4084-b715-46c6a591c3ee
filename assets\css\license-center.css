/* أنماط مركز التراخيص الموحد - RIDCODTOKEN License Center */

.ridcodtoken-license-center {
    max-width: 1200px;
}

.ridcodtoken-license-center .title-count {
    color: #666;
    font-weight: normal;
    font-size: 0.9em;
}

/* نظرة عامة */
.ridcodtoken-overview {
    margin: 20px 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-box {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.stat-box:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-box.total {
    border-left: 4px solid #0073aa;
}

.stat-box.active {
    border-left: 4px solid #46b450;
}

.stat-box.inactive {
    border-left: 4px solid #dc3232;
}

.stat-box.actions {
    border-left: 4px solid #ffb900;
}

.stat-icon {
    font-size: 24px;
    color: #666;
}

.stat-content h3 {
    margin: 0;
    font-size: 28px;
    font-weight: bold;
    color: #333;
}

.stat-content p {
    margin: 5px 0 0 0;
    color: #666;
    font-size: 14px;
}

/* تفعيل سريع */
.ridcodtoken-quick-activation {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 25px;
    margin: 30px 0;
}

.ridcodtoken-quick-activation h2 {
    margin-top: 0;
    color: #333;
}

.quick-form-container {
    margin: 20px 0;
}

.form-row {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr;
    gap: 15px;
    align-items: end;
}

.form-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-field input,
.form-field select {
    width: 100%;
}

/* قائمة الإضافات */
.ridcodtoken-plugins-list {
    margin: 40px 0;
}

.plugins-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.plugin-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
}

.plugin-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.plugin-card.active {
    border-left: 4px solid #46b450;
}

.plugin-card.inactive {
    border-left: 4px solid #dc3232;
}

.plugin-header {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 15px;
}

.plugin-icon {
    font-size: 24px;
    color: #666;
    margin-top: 5px;
}

.plugin-card.active .plugin-icon {
    color: #46b450;
}

.plugin-card.inactive .plugin-icon {
    color: #dc3232;
}

.plugin-info {
    flex: 1;
}

.plugin-title {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: #333;
}

.plugin-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    font-size: 12px;
}

.plugin-meta span {
    background: #f1f1f1;
    padding: 2px 8px;
    border-radius: 12px;
    color: #666;
}

.plugin-meta .active {
    background: #d4edda;
    color: #155724;
}

.plugin-meta .inactive {
    background: #f8d7da;
    color: #721c24;
}

.plugin-description {
    margin: 15px 0;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

.plugin-license-status {
    margin: 15px 0;
    padding: 12px;
    border-radius: 6px;
}

.license-active {
    background: #d4edda;
    border: 1px solid #c3e6cb;
}

.license-inactive {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
}

.status-badge {
    font-weight: 600;
    font-size: 14px;
}

.status-badge.active {
    color: #155724;
}

.status-badge.inactive {
    color: #721c24;
}

.last-check,
.license-key,
.license-required {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #666;
}

.plugin-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.plugin-actions .button {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
}

/* نتائج العمليات */
.activation-result,
.plugin-result {
    margin: 15px 0;
    padding: 10px;
    border-radius: 4px;
}

.activation-result.success,
.plugin-result.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.activation-result.error,
.plugin-result.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.activation-result.loading,
.plugin-result.loading {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

/* النافذة المنبثقة */
.ridcodtoken-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #fff;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px 20px 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.modal-header h3 {
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 0 20px 20px 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #ddd;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* معلومات إضافية */
.ridcodtoken-info-section {
    margin: 40px 0;
    padding: 30px 0;
    border-top: 1px solid #ddd;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.info-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.info-card h3 {
    margin-top: 0;
    color: #333;
}

.info-card ul {
    margin: 0;
    padding-left: 20px;
}

.info-card li {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* عدم وجود إضافات */
.no-plugins-found {
    text-align: center;
    padding: 40px 20px;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .plugins-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .plugin-actions {
        flex-direction: column;
    }
    
    .plugin-actions .button {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
