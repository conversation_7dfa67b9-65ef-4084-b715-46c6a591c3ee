/* أنماط إضافة اختصار الروابط - الواجهة الأمامية */

.ridcod-intermediate-page {
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
    line-height: 1.6;
}

/* تحسين العرض على الجوال */
@media (max-width: 768px) {
    .ridcod-intermediate-page .container {
        padding: 10px !important;
    }
    
    .ridcod-countdown-content {
        margin: 10px;
        padding: 20px !important;
    }
    
    .ridcod-countdown-timer {
        font-size: 36px !important;
    }
    
    .ridcod-redirect-section {
        padding: 20px !important;
        margin-top: 30px !important;
    }
    
    .ridcod-redirect-btn {
        padding: 15px 25px !important;
        font-size: 16px !important;
        width: 100%;
        display: block !important;
    }
}

/* تحسينات إضافية للعداد التنازلي */
.ridcod-countdown-overlay {
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.ridcod-countdown-content {
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ridcod-countdown-timer {
    animation: pulse 1s ease-in-out infinite alternate;
}

@keyframes pulse {
    from {
        opacity: 0.8;
    }
    to {
        opacity: 1;
    }
}

/* تأثيرات الأزرار */
.ridcod-continue-btn,
.ridcod-redirect-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.ridcod-continue-btn::before,
.ridcod-redirect-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.ridcod-continue-btn:hover::before,
.ridcod-redirect-btn:hover::before {
    left: 100%;
}

/* تحسين إمكانية الوصول */
.ridcod-continue-btn:focus,
.ridcod-redirect-btn:focus {
    outline: 3px solid rgba(0, 123, 255, 0.5);
    outline-offset: 2px;
}

/* تحسين النصوص العربية */
.ridcod-countdown-content h2,
.ridcod-countdown-message,
.ridcod-redirect-section h3,
.ridcod-redirect-section p {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
    font-weight: 600;
}

/* تأثير الظلال والإضاءة */
.ridcod-countdown-content {
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 10px 20px rgba(0, 0, 0, 0.05);
}

.ridcod-redirect-section {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e1e8ed;
}

/* تحسين الألوان للضوء الداكن */
@media (prefers-color-scheme: dark) {
    .ridcod-countdown-content {
        background: #2c3e50;
        color: #ecf0f1;
    }
    
    .ridcod-redirect-section {
        background: #34495e;
        color: #ecf0f1;
        border-color: #4a5f7a;
    }
}

/* تأثيرات الحركة للقسم الأخير */
.ridcod-redirect-section {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسين التدرجات */
.ridcod-continue-btn {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.ridcod-continue-btn:hover {
    background: linear-gradient(135deg, #219a52, #27ae60);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

.ridcod-redirect-btn {
    background: linear-gradient(135deg, #3498db, #5dade2);
}

.ridcod-redirect-btn:hover {
    background: linear-gradient(135deg, #2980b9, #3498db);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}
